#!/bin/bash

# LifeMindML Deployment Script
# Automates the deployment process for different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="lifemindml"
DOCKER_REGISTRY="your-registry.com"  # Replace with your registry
VERSION=${1:-latest}
ENVIRONMENT=${2:-production}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if required files exist
    required_files=("Dockerfile" "docker-compose.yml" "requirements.txt")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Required file $file not found."
            exit 1
        fi
    done
    
    log_success "All requirements satisfied."
}

build_images() {
    log_info "Building Docker images..."
    
    # Build the main application image
    docker build -t ${PROJECT_NAME}:${VERSION} .
    
    # Tag for registry if not latest
    if [[ "$VERSION" != "latest" ]]; then
        docker tag ${PROJECT_NAME}:${VERSION} ${PROJECT_NAME}:latest
    fi
    
    log_success "Docker images built successfully."
}

push_images() {
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "Pushing images to registry..."
        
        # Tag for registry
        docker tag ${PROJECT_NAME}:${VERSION} ${DOCKER_REGISTRY}/${PROJECT_NAME}:${VERSION}
        docker tag ${PROJECT_NAME}:${VERSION} ${DOCKER_REGISTRY}/${PROJECT_NAME}:latest
        
        # Push to registry
        docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}:${VERSION}
        docker push ${DOCKER_REGISTRY}/${PROJECT_NAME}:latest
        
        log_success "Images pushed to registry."
    else
        log_info "Skipping image push for $ENVIRONMENT environment."
    fi
}

setup_environment() {
    log_info "Setting up $ENVIRONMENT environment..."
    
    # Create necessary directories
    mkdir -p logs monitoring/grafana/{dashboards,datasources} nginx/ssl
    
    # Set appropriate permissions
    chmod 755 logs
    
    # Create environment-specific configuration
    case $ENVIRONMENT in
        "development")
            log_info "Setting up development environment..."
            cp docker-compose.dev.yml docker-compose.override.yml
            ;;
        "staging")
            log_info "Setting up staging environment..."
            # Add staging-specific setup here
            ;;
        "production")
            log_info "Setting up production environment..."
            # Ensure production configurations are secure
            if [[ ! -f ".env.production" ]]; then
                log_warning "Production environment file not found. Creating template..."
                create_production_env_template
            fi
            ;;
    esac
    
    log_success "Environment setup completed."
}

create_production_env_template() {
    cat > .env.production << EOF
# Production Environment Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-here
DATABASE_URL=****************************************/lifemindml
REDIS_URL=redis://redis:6379/0

# Security
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_ADMIN_PASSWORD=change-this-password

# Model Configuration
MODEL_CACHE_SIZE=1000
MAX_CONCURRENT_PREDICTIONS=10
EOF
    
    log_warning "Please update .env.production with your actual configuration values."
}

deploy_services() {
    log_info "Deploying services..."
    
    # Stop existing services
    docker-compose down --remove-orphans
    
    # Pull latest images (if using registry)
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker-compose pull
    fi
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    check_service_health
    
    log_success "Services deployed successfully."
}

check_service_health() {
    log_info "Checking service health..."
    
    services=("lifemindml-api" "lifemindml-frontend" "redis")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "$service.*Up"; then
            log_success "$service is running."
        else
            log_error "$service is not running properly."
            docker-compose logs "$service"
            exit 1
        fi
    done
    
    # Test API endpoint
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "API health check passed."
    else
        log_error "API health check failed."
        exit 1
    fi
}

run_tests() {
    log_info "Running deployment tests..."
    
    # Basic connectivity tests
    test_endpoints=(
        "http://localhost:8000/health"
        "http://localhost:8000/"
        "http://localhost:8501"
    )
    
    for endpoint in "${test_endpoints[@]}"; do
        if curl -f "$endpoint" > /dev/null 2>&1; then
            log_success "✓ $endpoint is accessible"
        else
            log_warning "✗ $endpoint is not accessible"
        fi
    done
    
    log_success "Deployment tests completed."
}

cleanup() {
    log_info "Cleaning up unused Docker resources..."
    docker system prune -f
    log_success "Cleanup completed."
}

show_status() {
    log_info "Deployment Status:"
    echo "===================="
    docker-compose ps
    echo ""
    log_info "Service URLs:"
    echo "- API: http://localhost:8000"
    echo "- Frontend: http://localhost:8501"
    echo "- Prometheus: http://localhost:9090"
    echo "- Grafana: http://localhost:3000"
    echo ""
    log_info "Logs can be viewed with: docker-compose logs -f [service-name]"
}

# Main deployment flow
main() {
    log_info "Starting LifeMindML deployment..."
    log_info "Version: $VERSION"
    log_info "Environment: $ENVIRONMENT"
    
    check_requirements
    setup_environment
    build_images
    push_images
    deploy_services
    run_tests
    cleanup
    show_status
    
    log_success "🎉 LifeMindML deployment completed successfully!"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "build")
        check_requirements
        build_images
        ;;
    "test")
        run_tests
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [command] [version] [environment]"
        echo ""
        echo "Commands:"
        echo "  deploy    - Full deployment (default)"
        echo "  build     - Build Docker images only"
        echo "  test      - Run deployment tests"
        echo "  status    - Show deployment status"
        echo "  cleanup   - Clean up Docker resources"
        echo "  help      - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 deploy v1.0.0 production"
        echo "  $0 build latest development"
        echo "  $0 test"
        ;;
    *)
        log_error "Unknown command: $1"
        echo "Use '$0 help' for usage information."
        exit 1
        ;;
esac
