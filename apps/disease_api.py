from fastapi import FastAPI, UploadFile, HTTPException
from src.disease_model import load_model
from torchvision import transforms
from PIL import Image
import torch
import io

app = FastAPI(
    title="Disease Detection API",
    description="Classify chest X-rays for TB detection",
    version="1.0.0"
)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = load_model()
if model is None:
    raise RuntimeError("❌ Failed to load TB detector model!")

transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406],
                         [0.229, 0.224, 0.225])
])

@app.post("/predict_tb")
async def predict_tb(file: UploadFile):
    contents = await file.read()
    try:
        image = Image.open(io.BytesIO(contents)).convert("RGB")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid image uploaded: {e}")
    
    image = transform(image).unsqueeze(0).to(device)
    with torch.no_grad():
        output = model(image)
        prob = torch.sigmoid(output).item()
    prediction = "TB Positive" if prob > 0.5 else "TB Negative"
    return {"prediction": prediction, "probability": prob}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8003)
