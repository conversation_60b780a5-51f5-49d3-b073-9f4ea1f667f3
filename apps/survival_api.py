from fastapi import FastAPI
from pydantic import BaseModel
from src.survival_model import predict_survival

app = FastAPI(
    title="Lifespan Survival API",
    description="Predicts patient survival curves from clinical data.",
    version="1.0.0"
)

class SurvivalInput(BaseModel):
    age: float
    anaemia: int
    creatinine_phosphokinase: float
    diabetes: int
    ejection_fraction: float
    high_blood_pressure: int
    platelets: float
    serum_creatinine: float
    serum_sodium: float
    sex: int
    smoking: int
    time: float  # required for input schema; ignored in prediction

@app.post("/predict_survival")
async def get_survival(input: SurvivalInput):
    """
    Predicts survival curve given clinical features.
    """
    try:
        features = input.dict()
        pred = predict_survival(features)
        return {
            "input": features,
            "survival_curve": pred
        }
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8002)
