from fastapi import FastAPI
from pydantic import BaseModel
from src.lifestyle_model import predict_bio_age

app = FastAPI(
    title="Lifestyle Optimizer API",
    description="Predict biological age based on lifestyle metrics.",
    version="1.0.0"
)

class LifestyleInput(BaseModel):
    sleep_hours: float
    steps: int
    calories: float
    protein: float

@app.post("/predict_bio_age")
async def predict_biological_age(input: LifestyleInput):
    """
    Predicts biological age from user lifestyle inputs.
    """
    try:
        features = {
            "calories": input.calories,
            "protein": input.protein,
            "sleep_hours": input.sleep_hours,
            "steps": input.steps
        }
        bio_age = predict_bio_age(features)
        return {
            "input": features,
            "predicted_biological_age": bio_age
        }
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8001)
