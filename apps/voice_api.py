from fastapi import FastAP<PERSON>, UploadFile, HTTPException
import torch
import os
from module.voice_preprocessing import preprocess_audio_librosa
from src.voice_model import CNNEmotionClassifier

app = FastAPI(title="Voice Emotion API")

# Emotion index-to-label mapping (RAVDESS standard)
EMOTION_LABELS = {
    0: "Neutral",
    1: "Calm",
    2: "Happy",
    3: "Sad",
    4: "Angry",
    5: "Fearful",
    6: "Disgust",
    7: "Surprised",
}

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = CNNEmotionClassifier().to(device)
model.load_state_dict(torch.load("model/voice_emotion.pth", map_location=device))
model.eval()

@app.post("/predict_emotion")
async def predict_emotion(file: UploadFile):
    temp_path = "temp.wav"
    try:
        contents = await file.read()
        if not contents:
            raise HTTPException(status_code=400, detail="Uploaded file is empty.")

        with open(temp_path, "wb") as f:
            f.write(contents)

        # Shared preprocessing for consistency
        mfcc_tensor = preprocess_audio_librosa(temp_path, n_mfcc=40, max_len=200).to(device)

        with torch.no_grad():
            outputs = model(mfcc_tensor)
            _, predicted = torch.max(outputs, 1)

        emotion_idx = int(predicted.item())
        emotion_label = EMOTION_LABELS.get(emotion_idx, "Unknown")

        return {
            "predicted_emotion": emotion_idx,
            "emotion_label": emotion_label
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

    finally:
        if os.path.exists(temp_path):
            os.remove(temp_path)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8005)

