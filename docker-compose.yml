version: '3.8'

services:
  # Main LifeMindML API service
  lifemindml-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: lifemindml-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/home/<USER>
      - LOG_LEVEL=INFO
      - MODEL_PATH=/home/<USER>/models
    volumes:
      - ./models:/home/<USER>/models:ro
      - ./data:/home/<USER>/data:ro
      - ./logs:/home/<USER>/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - lifemindml-network
    depends_on:
      - redis
      - prometheus

  # Streamlit frontend service
  lifemindml-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: application
    container_name: lifemindml-frontend
    ports:
      - "8501:8501"
    environment:
      - PYTHONPATH=/home/<USER>
      - API_BASE_URL=http://lifemindml-api:8000
    volumes:
      - ./models:/home/<USER>/models:ro
      - ./data:/home/<USER>/data:ro
    command: ["streamlit", "run", "app/streamlit_app.py", "--server.address", "0.0.0.0", "--server.port", "8501"]
    restart: unless-stopped
    networks:
      - lifemindml-network
    depends_on:
      - lifemindml-api

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: lifemindml-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - lifemindml-network
    command: redis-server --appendonly yes

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: lifemindml-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - lifemindml-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: lifemindml-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=lifemindml123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    networks:
      - lifemindml-network
    depends_on:
      - prometheus

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: lifemindml-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    restart: unless-stopped
    networks:
      - lifemindml-network
    depends_on:
      - lifemindml-api
      - lifemindml-frontend

volumes:
  redis-data:
  prometheus-data:
  grafana-data:

networks:
  lifemindml-network:
    driver: bridge
