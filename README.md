# 🧠 LifeMindML - AI-Driven Personalized Health & Lifespan Optimization Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://www.docker.com/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104.1-green.svg)](https://fastapi.tiangolo.com/)
[![Streamlit](https://img.shields.io/badge/Streamlit-1.28.0-red.svg)](https://streamlit.io/)

**LifeMindML** is a comprehensive AI/ML-powered platform that combines medical data science, predictive analytics, and biomedical knowledge graphs to help users and researchers predict, monitor, and optimize lifespan, health conditions, and lifestyle factors. It empowers healthcare professionals, individuals, and research teams with intelligent tools for precision health and longevity science.

## 🌟 Key Features

### ✅ **FULLY IMPLEMENTED MODULES**

| Module | Description | Technology Stack | Status |
|--------|-------------|------------------|--------|
| 🧪 **Drug Lifespan Classifier** | Predicts lifespan extension probability using SMILES notation | RDKit • RandomForest • Morgan Fingerprints | ✅ Complete |
| 🩻 **Disease Detection** | TB detection from chest X-rays using deep learning | ResNet18 • PyTorch • Medical Imaging | ✅ Complete |
| 🧘 **Lifestyle Optimizer** | Biological age prediction with personalized recommendations | Scikit-learn • NHANES • Health Analytics | ✅ Complete |
| 📈 **Survival Analysis** | Survival curves using Cox proportional hazards models | Lifelines • Cox Models • Clinical Data | ✅ Complete |
| 🧏 **Voice Emotion Detection** | Mental health indicators from voice audio analysis | CNN • MFCC • Audio Processing | ✅ Complete |

### 🆕 **NEW IMPLEMENTATIONS**

| Component | Description | Status |
|-----------|-------------|--------|
| 🎨 **Streamlit Frontend** | Comprehensive web dashboard with all modules | ✅ Complete |
| 🔗 **Unified API Gateway** | Single FastAPI service combining all endpoints | ✅ Complete |
| 🛡️ **Input Validation** | Robust validation and error handling system | ✅ Complete |
| 🐳 **Docker Deployment** | Multi-stage containerization with orchestration | ✅ Complete |
| 📊 **Model Monitoring** | Performance tracking and automated retraining | ✅ Complete |

## 🚀 Quick Start

### Option 1: Docker Deployment (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-org/lifemind-ml.git
cd lifemind-ml

# Deploy with Docker Compose
./scripts/deploy.sh

# Access the services
# Frontend: http://localhost:8501
# API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Option 2: Local Development

```bash
# Install dependencies
pip install -r requirements.txt
pip install -r requirements-docker.txt

# Start the API server
python -m uvicorn src.unify:app --host 0.0.0.0 --port 8000 --reload

# Start the Streamlit frontend (in another terminal)
streamlit run app/streamlit_app.py --server.port 8501
```

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    🧠 LifeMindML Platform                       │
├─────────────────────────────────────────────────────────────────┤
│  🎨 Streamlit Frontend  │  🔗 Unified API Gateway               │
│  - Interactive Dashboard │  - FastAPI with OpenAPI              │
│  - Data Visualization   │  - Input Validation                   │
│  - File Upload Support  │  - Error Handling                     │
├─────────────────────────────────────────────────────────────────┤
│                    🤖 AI/ML Modules                             │
│  🧪 Drug Classifier │ 🩻 Disease Detection │ 🧘 Lifestyle      │
│  🧏 Voice Emotion   │ 📈 Survival Analysis │ 📊 Monitoring     │
├─────────────────────────────────────────────────────────────────┤
│                    🧬 Biomni Integration                        │
│  - LLM-powered biomedical reasoning                             │
│  - Scientific literature access                                │
│  - Advanced bioinformatics tools                               │
├─────────────────────────────────────────────────────────────────┤
│                    🐳 Deployment & Monitoring                   │
│  - Docker containerization                                     │
│  - Kubernetes orchestration                                    │
│  - Prometheus metrics                                          │
│  - Grafana dashboards                                          │
└─────────────────────────────────────────────────────────────────┘
```

🧬 FINAL SYSTEM ARCHITECTURE
plaintext
Copy
Edit
                               USER HEALTH PROFILE
                                 (age, gender, sleep, food, stress, compounds)
                                             │
  ┌──────────────────────────────────────────▼────────────────────────────────────┐
  │                              UNIFIED AI ENGINE                                │
  └────────────────────────────────┬──────────────────────────────────────────────┘
                                   │
       ┌───────────────────────────┼────────────────────────────┐
       │                           │                            │
┌──────▼──────┐       ┌────────────▼────────────┐    ┌──────────▼──────────┐
│ Drug Classifier │   │ Lifestyle Optimizer     │    │ Lifespan Predictor │
└──────┬──────┘       └────────────┬────────────┘    └──────────┬──────────┘
       │                            │                            │
       ▼                            ▼                            ▼
 ┌────────────┐           ┌────────────────┐          ┌────────────────────┐
 │ Molecule → ✓│           │ Suggest Diet  │          │ Survival Curve Plot│
 └────────────┘           │ Sleep Plan     │          └────────────────────┘
                          └────────────────┘
       │
 ┌─────▼─────────┐
 │ Disease Scan AI│ ←── X-ray, MRI, CT Input (Pneumonia, TB, Tumor)
 └─────┬─────────┘
       │
 ┌─────▼──────────────┐
 │ Voice Mental Health│ ←── Voice Sample Input
 └────────────────────┘

 OUTPUT:
 - Lifespan Curve
 - Disease Risk %
 - Mental Health Score
 - Drug Recommendations
 - Lifestyle Advice
📦 Module-wise Summary
1. 🧪 Drug Classifier (Anti-Ageing)
Input: SMILES (molecule string)

Output: Slow-ageing probability

Tools: RDKit, XGBoost, DeepChem, ChemBERTa

Dataset: DrugAge, ChEMBL

2. 🧘 Lifestyle Optimizer
Input: Sleep, food, steps, blood data

Output: Predicted biological age + personalized suggestions

Tools: scikit-learn, XGBoost, Streamlit

Dataset: Aging.ai, simulated health CSV

3. 📈 Lifespan Survival Model
Input: Full health profile

Output: Survival curve

Tools: lifelines, pycox, xgbse

Dataset: UK Biobank, Framingham, SEER

4. 🩻 Disease Detection from X-rays, MRIs, CTs
Input: Medical images

Output: Classification (e.g., pneumonia, tumor, TB)

Models: CNN, ResNet, EfficientNet, Vision Transformer

Tools: PyTorch, FastAI, Keras

Datasets:

Chest X-ray14 (NIH)

RSNA Pneumonia Dataset

BraTS MRI Dataset (brain tumors)

COVID-CT, TB-Xray datasets

5. 🧏 Voice-Based Mental Health Detection
Input: Voice recording

Output: Depression, stress score

Models: LSTM, CNN+MFCC, Wav2Vec2

Tools: librosa, torchaudio, transformers, PyTorch

Datasets:

DAIC-WOZ (depression)

MSP-Podcast (emotion/stress)

Coswara (Indian voice health data)

Simulated voice datasets

---

## 📖 Usage Guide

### 🎨 Web Interface (Streamlit)

Access the interactive dashboard at `http://localhost:8501`:

1. **🏠 Home Dashboard** - Platform overview and metrics
2. **🧪 Drug Classifier** - Enter SMILES strings for lifespan prediction
3. **🩻 Disease Detection** - Upload chest X-rays for TB detection
4. **🧘 Lifestyle Optimizer** - Input lifestyle factors for biological age
5. **📈 Survival Analysis** - Clinical data for survival curves
6. **🧏 Voice Emotion** - Upload audio for emotion detection
7. **📊 Analytics** - Performance monitoring and system health

### 🔗 API Usage

The unified API provides programmatic access to all modules:

#### Drug Lifespan Prediction
```python
import requests

response = requests.post("http://localhost:8000/api/v1/drug/predict",
    json={"smiles": "CN(C)C(=N)N=C(N)N"})  # Metformin
print(response.json())
```

#### Disease Detection
```python
import requests

with open("chest_xray.jpg", "rb") as f:
    response = requests.post("http://localhost:8000/api/v1/disease/predict",
        files={"file": f})
print(response.json())
```

### 📊 Monitoring & Analytics

Access monitoring endpoints:

- **Health Check**: `GET /health`
- **Performance Metrics**: `GET /api/v1/monitoring/performance/{model_name}`
- **Dashboard Data**: `GET /api/v1/monitoring/dashboard`
- **Prometheus Metrics**: `GET /api/v1/monitoring/metrics`

## 🐳 Deployment Options

### Development Environment
```bash
# Start development stack with hot reload
docker-compose -f docker-compose.dev.yml up -d
```

### Production Environment
```bash
# Deploy production stack
./scripts/deploy.sh deploy latest production
```

## 🔒 Security & Deployment

- ✅ **Container-ready**: Multi-stage Docker builds with security best practices
- ✅ **Input validation**: Comprehensive validation for all data types
- ✅ **Error handling**: Robust error handling and recovery mechanisms
- ✅ **Monitoring**: Real-time performance tracking and alerting
- ✅ **Scalable**: Kubernetes-ready with horizontal pod autoscaling

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.