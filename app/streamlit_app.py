import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
import json
import io
from PIL import Image
import librosa
import tempfile
import os
import sys

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import local modules
try:
    from drug_model import predict_lifespan_effect
    from lifestyle_model import predict_bio_age
    from survival_model import predict_survival
    from voice_model import CNNEmotionClassifier
    from disease_model import load_model as load_disease_model
except ImportError as e:
    st.error(f"Error importing modules: {e}")

# Page configuration
st.set_page_config(
    page_title="🧠 LifeMindML - AI Health Platform",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .module-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Main header
    st.markdown('<h1 class="main-header">🧠 LifeMindML</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">AI-Driven Personalized Health & Lifespan Optimization Platform</p>', unsafe_allow_html=True)

    # Sidebar navigation
    st.sidebar.title("🔬 Navigation")
    page = st.sidebar.selectbox(
        "Choose a Module",
        [
            "🏠 Home Dashboard",
            "🧪 Drug Lifespan Classifier",
            "🩻 Disease Detection",
            "🧘 Lifestyle Optimizer",
            "📈 Survival Analysis",
            "🧏 Voice Emotion Detection",
            "📊 Analytics Dashboard"
        ]
    )

    # Route to different pages
    if page == "🏠 Home Dashboard":
        show_home_dashboard()
    elif page == "🧪 Drug Lifespan Classifier":
        show_drug_classifier()
    elif page == "🩻 Disease Detection":
        show_disease_detection()
    elif page == "🧘 Lifestyle Optimizer":
        show_lifestyle_optimizer()
    elif page == "📈 Survival Analysis":
        show_survival_analysis()
    elif page == "🧏 Voice Emotion Detection":
        show_voice_emotion()
    elif page == "📊 Analytics Dashboard":
        show_analytics_dashboard()

def show_home_dashboard():
    """Display the main dashboard with overview of all modules"""
    st.markdown('<h2 class="module-header">🏠 Platform Overview</h2>', unsafe_allow_html=True)

    # Key metrics row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="🧪 Drug Compounds",
            value="2,674",
            delta="DrugAge Dataset"
        )

    with col2:
        st.metric(
            label="🩻 Medical Images",
            value="11,000+",
            delta="TB Detection Ready"
        )

    with col3:
        st.metric(
            label="🧘 Lifestyle Factors",
            value="50+",
            delta="NHANES Data"
        )

    with col4:
        st.metric(
            label="🧏 Voice Emotions",
            value="8 Classes",
            delta="RAVDESS Dataset"
        )

    # Feature overview
    st.markdown("---")
    st.markdown("### 🚀 Available Modules")

    # Create feature cards
    features = [
        {
            "icon": "🧪",
            "title": "Drug Lifespan Classifier",
            "description": "Predict if a molecule extends lifespan using SMILES strings and ML models trained on DrugAge dataset.",
            "tech": "RDKit • RandomForest • Morgan Fingerprints"
        },
        {
            "icon": "🩻",
            "title": "Disease Detection",
            "description": "Classify chest X-rays for tuberculosis detection using deep learning computer vision models.",
            "tech": "ResNet18 • PyTorch • Medical Imaging"
        },
        {
            "icon": "🧘",
            "title": "Lifestyle Optimizer",
            "description": "Predict biological age and get personalized recommendations based on lifestyle factors.",
            "tech": "Scikit-learn • NHANES • Health Analytics"
        },
        {
            "icon": "📈",
            "title": "Survival Analysis",
            "description": "Generate survival curves and longevity predictions using Cox proportional hazards models.",
            "tech": "Lifelines • Cox Models • Clinical Data"
        },
        {
            "icon": "🧏",
            "title": "Voice Emotion Detection",
            "description": "Analyze emotional state and mental health indicators from voice audio samples.",
            "tech": "CNN • MFCC • Audio Processing"
        }
    ]

    # Display features in a grid
    for i in range(0, len(features), 2):
        cols = st.columns(2)
        for j, col in enumerate(cols):
            if i + j < len(features):
                feature = features[i + j]
                with col:
                    st.markdown(f"""
                    <div class="metric-card">
                        <h3>{feature['icon']} {feature['title']}</h3>
                        <p>{feature['description']}</p>
                        <small><strong>Tech Stack:</strong> {feature['tech']}</small>
                    </div>
                    """, unsafe_allow_html=True)

    # Integration info
    st.markdown("---")
    st.markdown("### 🧬 Biomni Integration")
    st.info("""
    **LifeMindML is integrated with Stanford's Biomni biomedical agent framework:**
    - 🤖 LLM-powered biomedical reasoning
    - 📚 Access to scientific literature and databases
    - 🔬 Advanced bioinformatics and pharmacology tools
    - 🧪 Custom LifeMind drug prediction tool available in Biomni
    """)

def show_drug_classifier():
    """Drug Lifespan Classifier Interface"""
    st.markdown('<h2 class="module-header">🧪 Drug Lifespan Classifier</h2>', unsafe_allow_html=True)

    st.markdown("""
    **Predict whether a molecule has lifespan-extending properties using SMILES notation.**

    This model is trained on the DrugAge database and uses molecular fingerprints to classify compounds.
    """)

    # Input section
    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("📝 Input Molecule")

        # SMILES input
        smiles_input = st.text_input(
            "Enter SMILES string:",
            value="CN(C)C(=N)N=C(N)N",  # Metformin as example
            help="Enter the SMILES notation of the molecule you want to analyze"
        )

        # Example molecules
        st.markdown("**Example molecules:**")
        examples = {
            "Metformin (Diabetes drug)": "CN(C)C(=N)N=C(N)N",
            "Aspirin": "CC(=O)OC1=CC=CC=C1C(=O)O",
            "Resveratrol": "C1=CC(=CC=C1C=CC2=CC(=CC(=C2)O)O)O",
            "Rapamycin": "CC1CCC2CC(C(=CC=CC=CC(CC(C(=O)C(C(C(=CC(C(=O)CC(OC(=O)C3CCCCN3C(=O)C(=O)C1(O2)O)C(C)CC4CCC(C(C4)OC)O)C)C)O)OC)C)C)C)OC"
        }

        selected_example = st.selectbox("Or choose an example:", ["Custom"] + list(examples.keys()))
        if selected_example != "Custom":
            smiles_input = examples[selected_example]
            st.text_input("SMILES:", value=smiles_input, disabled=True)

    with col2:
        st.subheader("🔬 Molecule Info")
        if smiles_input:
            try:
                from rdkit import Chem
                from rdkit.Chem import Descriptors

                mol = Chem.MolFromSmiles(smiles_input)
                if mol:
                    st.success("✅ Valid SMILES")
                    st.write(f"**Molecular Weight:** {Descriptors.MolWt(mol):.2f}")
                    st.write(f"**LogP:** {Descriptors.MolLogP(mol):.2f}")
                    st.write(f"**H-Bond Donors:** {Descriptors.NumHDonors(mol)}")
                    st.write(f"**H-Bond Acceptors:** {Descriptors.NumHAcceptors(mol)}")
                else:
                    st.error("❌ Invalid SMILES")
            except Exception as e:
                st.error(f"Error analyzing molecule: {e}")

    # Prediction section
    if st.button("🚀 Predict Lifespan Effect", type="primary"):
        if smiles_input:
            try:
                with st.spinner("Analyzing molecule..."):
                    probability = predict_lifespan_effect(smiles_input)

                # Display results
                st.markdown("---")
                st.subheader("📊 Prediction Results")

                # Probability gauge
                fig = go.Figure(go.Indicator(
                    mode = "gauge+number+delta",
                    value = probability * 100,
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    title = {'text': "Lifespan Extension Probability (%)"},
                    delta = {'reference': 50},
                    gauge = {
                        'axis': {'range': [None, 100]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 25], 'color': "lightgray"},
                            {'range': [25, 50], 'color': "yellow"},
                            {'range': [50, 75], 'color': "orange"},
                            {'range': [75, 100], 'color': "green"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 90
                        }
                    }
                ))
                fig.update_layout(height=400)
                st.plotly_chart(fig, use_container_width=True)

                # Interpretation
                if probability > 0.7:
                    st.markdown(f"""
                    <div class="success-box">
                        <strong>🎉 High Potential!</strong><br>
                        This molecule shows strong potential for lifespan extension (Probability: {probability:.3f}).
                        Consider further research and validation.
                    </div>
                    """, unsafe_allow_html=True)
                elif probability > 0.5:
                    st.markdown(f"""
                    <div class="warning-box">
                        <strong>⚠️ Moderate Potential</strong><br>
                        This molecule shows moderate potential (Probability: {probability:.3f}).
                        Additional studies may be warranted.
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.info(f"**Low Potential:** This molecule shows limited evidence for lifespan extension (Probability: {probability:.3f}).")

            except Exception as e:
                st.error(f"Prediction failed: {e}")
        else:
            st.warning("Please enter a SMILES string.")

def show_disease_detection():
    """Disease Detection Interface"""
    st.markdown('<h2 class="module-header">🩻 Disease Detection</h2>', unsafe_allow_html=True)

    st.markdown("""
    **Upload chest X-ray images for tuberculosis (TB) detection using deep learning.**

    This model uses a ResNet18 architecture trained on medical imaging data.
    """)

    # File upload
    uploaded_file = st.file_uploader(
        "Choose a chest X-ray image...",
        type=['png', 'jpg', 'jpeg'],
        help="Upload a chest X-ray image in PNG, JPG, or JPEG format"
    )

    if uploaded_file is not None:
        # Display image
        col1, col2 = st.columns([1, 1])

        with col1:
            st.subheader("📷 Uploaded Image")
            image = Image.open(uploaded_file)
            st.image(image, caption="Chest X-ray", use_column_width=True)

            # Image info
            st.write(f"**Image Size:** {image.size}")
            st.write(f"**Image Mode:** {image.mode}")

        with col2:
            st.subheader("🔍 Analysis")

            if st.button("🚀 Analyze Image", type="primary"):
                try:
                    with st.spinner("Analyzing chest X-ray..."):
                        # Here you would call your disease detection model
                        # For now, we'll simulate the prediction
                        import random
                        import time
                        time.sleep(2)  # Simulate processing time

                        # Simulated prediction (replace with actual model call)
                        probability = random.uniform(0.1, 0.9)
                        prediction = "TB Positive" if probability > 0.5 else "TB Negative"

                    # Display results
                    st.markdown("---")
                    st.subheader("📊 Detection Results")

                    # Result display
                    if prediction == "TB Positive":
                        st.error(f"⚠️ **{prediction}** (Confidence: {probability:.3f})")
                        st.markdown("""
                        <div class="warning-box">
                            <strong>Medical Attention Required</strong><br>
                            The model detected potential signs of tuberculosis.
                            Please consult with a healthcare professional immediately.
                        </div>
                        """, unsafe_allow_html=True)
                    else:
                        st.success(f"✅ **{prediction}** (Confidence: {1-probability:.3f})")
                        st.info("No signs of tuberculosis detected. However, this is not a substitute for professional medical diagnosis.")

                    # Confidence visualization
                    fig = px.bar(
                        x=['TB Negative', 'TB Positive'],
                        y=[1-probability, probability],
                        title="Detection Confidence",
                        color=['TB Negative', 'TB Positive'],
                        color_discrete_map={'TB Negative': 'green', 'TB Positive': 'red'}
                    )
                    fig.update_layout(showlegend=False, yaxis_title="Probability")
                    st.plotly_chart(fig, use_container_width=True)

                except Exception as e:
                    st.error(f"Analysis failed: {e}")

    # Disclaimer
    st.markdown("---")
    st.warning("""
    **⚠️ Medical Disclaimer:** This tool is for research and educational purposes only.
    It should not be used as a substitute for professional medical diagnosis or treatment.
    Always consult with qualified healthcare professionals for medical decisions.
    """)

def show_lifestyle_optimizer():
    """Lifestyle Optimizer Interface"""
    st.markdown('<h2 class="module-header">🧘 Lifestyle Optimizer</h2>', unsafe_allow_html=True)

    st.markdown("""
    **Get your biological age prediction and personalized health recommendations.**

    Input your lifestyle factors to receive AI-powered insights for optimal health and longevity.
    """)

    # Input form
    with st.form("lifestyle_form"):
        st.subheader("📝 Your Lifestyle Profile")

        col1, col2 = st.columns(2)

        with col1:
            sleep_hours = st.slider(
                "💤 Sleep Hours per Night",
                min_value=4.0,
                max_value=12.0,
                value=7.5,
                step=0.5,
                help="Average hours of sleep per night"
            )

            steps = st.number_input(
                "🚶 Daily Steps",
                min_value=0,
                max_value=50000,
                value=8000,
                step=500,
                help="Average daily step count"
            )

        with col2:
            calories = st.number_input(
                "🍽️ Daily Calories",
                min_value=1000.0,
                max_value=5000.0,
                value=2200.0,
                step=50.0,
                help="Average daily caloric intake"
            )

            protein = st.number_input(
                "🥩 Daily Protein (g)",
                min_value=20.0,
                max_value=300.0,
                value=80.0,
                step=5.0,
                help="Average daily protein intake in grams"
            )

        submitted = st.form_submit_button("🚀 Analyze My Lifestyle", type="primary")

    if submitted:
        try:
            with st.spinner("Analyzing your lifestyle profile..."):
                features = {
                    "sleep_hours": sleep_hours,
                    "steps": steps,
                    "calories": calories,
                    "protein": protein
                }

                bio_age = predict_bio_age(features)

            # Results display
            st.markdown("---")
            st.subheader("📊 Your Health Analysis")

            # Biological age display
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    label="🧬 Predicted Biological Age",
                    value=f"{bio_age:.1f} years",
                    delta=f"{bio_age - 35:.1f} vs avg 35yr old"  # Assuming 35 as reference
                )

            with col2:
                health_score = max(0, min(100, 100 - abs(bio_age - 25) * 2))  # Simple health score
                st.metric(
                    label="💪 Health Score",
                    value=f"{health_score:.0f}/100",
                    delta="Good" if health_score > 70 else "Needs Improvement"
                )

            with col3:
                longevity_potential = "High" if bio_age < 30 else "Moderate" if bio_age < 40 else "Low"
                st.metric(
                    label="🌟 Longevity Potential",
                    value=longevity_potential,
                    delta="Based on lifestyle factors"
                )

            # Lifestyle factor analysis
            st.subheader("📈 Lifestyle Factor Analysis")

            # Create radar chart for lifestyle factors
            categories = ['Sleep Quality', 'Physical Activity', 'Nutrition', 'Metabolic Health']

            # Normalize values for radar chart (0-100 scale)
            sleep_score = min(100, max(0, (sleep_hours - 4) / 4 * 100))
            activity_score = min(100, steps / 10000 * 100)
            nutrition_score = min(100, max(0, 100 - abs(calories - 2200) / 22))
            protein_score = min(100, protein / 100 * 100)

            values = [sleep_score, activity_score, nutrition_score, protein_score]

            fig = go.Figure()
            fig.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name='Your Profile'
            ))
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )),
                showlegend=True,
                title="Lifestyle Factor Scores"
            )
            st.plotly_chart(fig, use_container_width=True)

            # Personalized recommendations
            st.subheader("💡 Personalized Recommendations")

            recommendations = []

            if sleep_hours < 7:
                recommendations.append("😴 **Improve Sleep:** Aim for 7-9 hours of quality sleep per night. Consider a consistent bedtime routine.")
            elif sleep_hours > 9:
                recommendations.append("⏰ **Optimize Sleep:** While you're getting plenty of sleep, ensure it's quality sleep. Too much sleep can also impact health.")

            if steps < 8000:
                recommendations.append("🚶 **Increase Activity:** Try to reach 8,000-10,000 steps daily. Consider taking walking breaks or using stairs.")
            elif steps > 15000:
                recommendations.append("🏃 **Great Activity Level:** You're very active! Make sure to include rest days for recovery.")

            if calories < 1800:
                recommendations.append("🍽️ **Increase Calories:** You may be under-eating. Ensure adequate nutrition for your activity level.")
            elif calories > 2800:
                recommendations.append("⚖️ **Monitor Calories:** Consider portion control and focus on nutrient-dense foods.")

            if protein < 60:
                recommendations.append("🥩 **Boost Protein:** Aim for 0.8-1.2g per kg body weight. Include lean meats, fish, legumes, or protein supplements.")

            if not recommendations:
                recommendations.append("🎉 **Great Job!** Your lifestyle factors are well-balanced. Keep up the good work!")

            for rec in recommendations:
                st.markdown(f"- {rec}")

        except Exception as e:
            st.error(f"Analysis failed: {e}")

def show_survival_analysis():
    """Survival Analysis Interface"""
    st.markdown('<h2 class="module-header">📈 Survival Analysis</h2>', unsafe_allow_html=True)

    st.markdown("""
    **Predict survival curves based on clinical and lifestyle factors.**

    This model uses Cox proportional hazards to estimate survival probabilities over time.
    """)

    # Input form
    with st.form("survival_form"):
        st.subheader("🏥 Clinical Profile")

        col1, col2, col3 = st.columns(3)

        with col1:
            age = st.number_input("Age", min_value=18, max_value=100, value=60)
            sex = st.selectbox("Sex", ["Male", "Female"])
            smoking = st.selectbox("Smoking Status", ["Non-smoker", "Smoker"])
            diabetes = st.selectbox("Diabetes", ["No", "Yes"])

        with col2:
            anaemia = st.selectbox("Anaemia", ["No", "Yes"])
            high_bp = st.selectbox("High Blood Pressure", ["No", "Yes"])
            ejection_fraction = st.slider("Ejection Fraction (%)", 10, 80, 35)

        with col3:
            creatinine_phosphokinase = st.number_input("Creatinine Phosphokinase", 0, 1000, 200)
            serum_creatinine = st.number_input("Serum Creatinine", 0.5, 5.0, 1.2, step=0.1)
            serum_sodium = st.number_input("Serum Sodium", 120, 150, 137)
            platelets = st.number_input("Platelets", 100000, 500000, 250000, step=10000)

        submitted = st.form_submit_button("📊 Generate Survival Analysis", type="primary")

    if submitted:
        try:
            with st.spinner("Generating survival analysis..."):
                # Prepare features
                features = {
                    'age': float(age),
                    'anaemia': 1 if anaemia == "Yes" else 0,
                    'creatinine_phosphokinase': float(creatinine_phosphokinase),
                    'diabetes': 1 if diabetes == "Yes" else 0,
                    'ejection_fraction': float(ejection_fraction),
                    'high_blood_pressure': 1 if high_bp == "Yes" else 0,
                    'platelets': float(platelets),
                    'serum_creatinine': float(serum_creatinine),
                    'serum_sodium': float(serum_sodium),
                    'sex': 1 if sex == "Male" else 0,
                    'smoking': 1 if smoking == "Smoker" else 0,
                    'time': 100  # Required for schema but not used in prediction
                }

                survival_data = predict_survival(features)

            # Display results
            st.markdown("---")
            st.subheader("📊 Survival Analysis Results")

            # Survival curve plot
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=survival_data['time_days'],
                y=survival_data['survival_probability'],
                mode='lines',
                name='Survival Probability',
                line=dict(color='blue', width=3)
            ))

            fig.update_layout(
                title='Predicted Survival Curve',
                xaxis_title='Time (Days)',
                yaxis_title='Survival Probability',
                yaxis=dict(range=[0, 1]),
                hovermode='x unified'
            )

            st.plotly_chart(fig, use_container_width=True)

            # Key statistics
            col1, col2, col3 = st.columns(3)

            # Find median survival time (where probability drops to 0.5)
            median_survival = None
            for i, prob in enumerate(survival_data['survival_probability']):
                if prob <= 0.5:
                    median_survival = survival_data['time_days'][i]
                    break

            with col1:
                if median_survival:
                    st.metric("📅 Median Survival", f"{median_survival:.0f} days", f"{median_survival/365.25:.1f} years")
                else:
                    st.metric("📅 Median Survival", "> 2 years", "Good prognosis")

            with col2:
                prob_1_year = None
                for i, time in enumerate(survival_data['time_days']):
                    if time >= 365:
                        prob_1_year = survival_data['survival_probability'][i]
                        break
                if prob_1_year:
                    st.metric("📊 1-Year Survival", f"{prob_1_year:.1%}", "Probability")

            with col3:
                # Risk assessment
                if median_survival and median_survival < 365:
                    risk_level = "High"
                elif median_survival and median_survival < 730:
                    risk_level = "Moderate"
                else:
                    risk_level = "Low"
                st.metric("⚠️ Risk Level", risk_level, "Assessment")

        except Exception as e:
            st.error(f"Analysis failed: {e}")

def show_voice_emotion():
    """Voice Emotion Detection Interface"""
    st.markdown('<h2 class="module-header">🧏 Voice Emotion Detection</h2>', unsafe_allow_html=True)

    st.markdown("""
    **Analyze emotional state and mental health indicators from voice samples.**

    Upload an audio file to detect emotions using advanced audio processing and machine learning.
    """)

    # File upload
    uploaded_audio = st.file_uploader(
        "Choose an audio file...",
        type=['wav', 'mp3', 'flac'],
        help="Upload an audio file in WAV, MP3, or FLAC format"
    )

    # Emotion labels
    emotion_labels = {
        0: "😐 Neutral",
        1: "😌 Calm",
        2: "😊 Happy",
        3: "😢 Sad",
        4: "😠 Angry",
        5: "😨 Fearful",
        6: "🤢 Disgust",
        7: "😲 Surprised"
    }

    if uploaded_audio is not None:
        # Display audio player
        st.subheader("🎵 Audio Sample")
        st.audio(uploaded_audio, format='audio/wav')

        # Audio analysis
        col1, col2 = st.columns([1, 1])

        with col1:
            if st.button("🎯 Analyze Emotion", type="primary"):
                try:
                    with st.spinner("Analyzing voice emotion..."):
                        # Save uploaded file temporarily
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                            tmp_file.write(uploaded_audio.getvalue())
                            temp_path = tmp_file.name

                        # Load and analyze audio
                        y, sr = librosa.load(temp_path, sr=None)

                        # Extract basic audio features for display
                        duration = len(y) / sr
                        rms_energy = np.sqrt(np.mean(y**2))
                        zero_crossing_rate = np.mean(librosa.feature.zero_crossing_rate(y))

                        # Simulate emotion prediction (replace with actual model)
                        import random
                        predicted_emotion_idx = random.randint(0, 7)
                        confidence = random.uniform(0.6, 0.95)

                        # Clean up temp file
                        os.unlink(temp_path)

                    # Display results
                    st.markdown("---")
                    st.subheader("🎭 Emotion Analysis Results")

                    # Main prediction
                    predicted_emotion = emotion_labels[predicted_emotion_idx]
                    st.success(f"**Detected Emotion:** {predicted_emotion}")
                    st.info(f"**Confidence:** {confidence:.1%}")

                    # Audio characteristics
                    st.subheader("🔊 Audio Characteristics")
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("⏱️ Duration", f"{duration:.2f}s")
                    with col2:
                        st.metric("📊 RMS Energy", f"{rms_energy:.4f}")
                    with col3:
                        st.metric("🌊 Zero Crossing Rate", f"{zero_crossing_rate:.4f}")

                    # Emotion probability distribution
                    st.subheader("📊 Emotion Probability Distribution")

                    # Generate simulated probabilities for all emotions
                    probs = np.random.dirichlet(np.ones(8) * 0.5)  # Random probabilities that sum to 1
                    probs[predicted_emotion_idx] = confidence  # Set the predicted emotion to have highest probability
                    probs = probs / np.sum(probs)  # Renormalize

                    # Create bar chart
                    emotion_names = [emotion_labels[i].split(' ')[1] for i in range(8)]  # Remove emoji for cleaner chart

                    fig = px.bar(
                        x=emotion_names,
                        y=probs,
                        title="Emotion Classification Probabilities",
                        color=probs,
                        color_continuous_scale="viridis"
                    )
                    fig.update_layout(
                        xaxis_title="Emotions",
                        yaxis_title="Probability",
                        showlegend=False
                    )
                    st.plotly_chart(fig, use_container_width=True)

                    # Mental health insights
                    st.subheader("🧠 Mental Health Insights")

                    if predicted_emotion_idx in [3, 4, 5]:  # Sad, Angry, Fearful
                        st.warning("""
                        **⚠️ Potential Stress Indicators Detected**

                        The analysis suggests possible signs of emotional distress. Consider:
                        - Speaking with a mental health professional
                        - Practicing stress management techniques
                        - Maintaining social connections
                        """)
                    elif predicted_emotion_idx in [0, 1]:  # Neutral, Calm
                        st.info("""
                        **😌 Balanced Emotional State**

                        Your voice indicates a calm and balanced emotional state.
                        Continue maintaining good mental health practices.
                        """)
                    else:  # Happy, Surprised
                        st.success("""
                        **😊 Positive Emotional State**

                        Your voice indicates positive emotions.
                        Keep up the activities that contribute to your well-being!
                        """)

                except Exception as e:
                    st.error(f"Analysis failed: {e}")

        with col2:
            st.subheader("📈 Audio Waveform")
            if uploaded_audio:
                try:
                    # Create a simple waveform visualization
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                        tmp_file.write(uploaded_audio.getvalue())
                        temp_path = tmp_file.name

                    y, sr = librosa.load(temp_path, sr=None)
                    time = np.linspace(0, len(y) / sr, len(y))

                    # Downsample for visualization if too long
                    if len(y) > 10000:
                        step = len(y) // 10000
                        y = y[::step]
                        time = time[::step]

                    fig = go.Figure()
                    fig.add_trace(go.Scatter(x=time, y=y, mode='lines', name='Waveform'))
                    fig.update_layout(
                        title="Audio Waveform",
                        xaxis_title="Time (s)",
                        yaxis_title="Amplitude",
                        height=300
                    )
                    st.plotly_chart(fig, use_container_width=True)

                    os.unlink(temp_path)
                except Exception as e:
                    st.error(f"Could not display waveform: {e}")

    # Disclaimer
    st.markdown("---")
    st.warning("""
    **⚠️ Mental Health Disclaimer:** This tool is for research and educational purposes only.
    It should not be used as a substitute for professional mental health assessment or treatment.
    If you're experiencing mental health concerns, please consult with qualified healthcare professionals.
    """)

def show_analytics_dashboard():
    """Analytics Dashboard showing platform usage and model performance"""
    st.markdown('<h2 class="module-header">📊 Analytics Dashboard</h2>', unsafe_allow_html=True)

    st.markdown("""
    **Platform performance metrics and usage analytics.**

    Monitor model performance, usage patterns, and system health.
    """)

    # Simulated analytics data
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("🔬 Total Predictions", "12,847", "+234 today")
    with col2:
        st.metric("👥 Active Users", "1,523", "+45 this week")
    with col3:
        st.metric("⚡ Avg Response Time", "1.2s", "-0.3s improved")
    with col4:
        st.metric("✅ Model Accuracy", "94.2%", "+1.1% this month")

    # Usage by module
    st.subheader("📈 Usage by Module")

    module_usage = {
        'Drug Classifier': 3245,
        'Disease Detection': 2876,
        'Lifestyle Optimizer': 2134,
        'Survival Analysis': 1987,
        'Voice Emotion': 2605
    }

    fig = px.pie(
        values=list(module_usage.values()),
        names=list(module_usage.keys()),
        title="Predictions by Module (Last 30 Days)"
    )
    st.plotly_chart(fig, use_container_width=True)

    # Performance trends
    st.subheader("📊 Performance Trends")

    # Generate sample time series data
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    np.random.seed(42)

    # Simulate metrics
    daily_predictions = np.random.poisson(50, len(dates)) + np.sin(np.arange(len(dates)) * 2 * np.pi / 365) * 10 + 50
    accuracy_scores = 0.92 + 0.05 * np.sin(np.arange(len(dates)) * 2 * np.pi / 30) + np.random.normal(0, 0.01, len(dates))
    response_times = 1.0 + 0.3 * np.sin(np.arange(len(dates)) * 2 * np.pi / 7) + np.random.normal(0, 0.1, len(dates))

    # Create subplots
    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=('Daily Predictions', 'Model Accuracy', 'Response Time'),
        vertical_spacing=0.08
    )

    fig.add_trace(
        go.Scatter(x=dates, y=daily_predictions, name='Predictions', line=dict(color='blue')),
        row=1, col=1
    )

    fig.add_trace(
        go.Scatter(x=dates, y=accuracy_scores, name='Accuracy', line=dict(color='green')),
        row=2, col=1
    )

    fig.add_trace(
        go.Scatter(x=dates, y=response_times, name='Response Time', line=dict(color='red')),
        row=3, col=1
    )

    fig.update_layout(height=800, showlegend=False)
    fig.update_yaxes(title_text="Count", row=1, col=1)
    fig.update_yaxes(title_text="Accuracy", row=2, col=1)
    fig.update_yaxes(title_text="Seconds", row=3, col=1)

    st.plotly_chart(fig, use_container_width=True)

    # Model performance by module
    st.subheader("🎯 Model Performance by Module")

    performance_data = {
        'Module': ['Drug Classifier', 'Disease Detection', 'Lifestyle Optimizer', 'Survival Analysis', 'Voice Emotion'],
        'Accuracy': [0.943, 0.912, 0.876, 0.934, 0.889],
        'Precision': [0.938, 0.905, 0.882, 0.941, 0.894],
        'Recall': [0.947, 0.918, 0.871, 0.928, 0.885],
        'F1-Score': [0.942, 0.911, 0.876, 0.934, 0.889]
    }

    df_performance = pd.DataFrame(performance_data)

    fig = px.bar(
        df_performance.melt(id_vars=['Module'], var_name='Metric', value_name='Score'),
        x='Module',
        y='Score',
        color='Metric',
        barmode='group',
        title='Model Performance Metrics by Module'
    )
    fig.update_layout(yaxis=dict(range=[0.8, 1.0]))
    st.plotly_chart(fig, use_container_width=True)

    # System health
    st.subheader("🖥️ System Health")

    col1, col2 = st.columns(2)

    with col1:
        # CPU and Memory usage
        cpu_usage = np.random.uniform(20, 80)
        memory_usage = np.random.uniform(30, 70)

        fig = go.Figure()
        fig.add_trace(go.Indicator(
            mode = "gauge+number",
            value = cpu_usage,
            domain = {'x': [0, 0.5], 'y': [0, 1]},
            title = {'text': "CPU Usage (%)"},
            gauge = {'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [{'range': [0, 50], 'color': "lightgray"},
                             {'range': [50, 80], 'color': "yellow"},
                             {'range': [80, 100], 'color': "red"}]}
        ))

        fig.add_trace(go.Indicator(
            mode = "gauge+number",
            value = memory_usage,
            domain = {'x': [0.5, 1], 'y': [0, 1]},
            title = {'text': "Memory Usage (%)"},
            gauge = {'axis': {'range': [None, 100]},
                    'bar': {'color': "darkgreen"},
                    'steps': [{'range': [0, 50], 'color': "lightgray"},
                             {'range': [50, 80], 'color': "yellow"},
                             {'range': [80, 100], 'color': "red"}]}
        ))

        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        # Error rates
        st.markdown("**📊 Error Rates (Last 24h)**")
        error_data = {
            'Module': ['Drug Classifier', 'Disease Detection', 'Lifestyle Optimizer', 'Survival Analysis', 'Voice Emotion'],
            'Error Rate (%)': [0.2, 0.8, 0.3, 0.1, 0.5]
        }

        df_errors = pd.DataFrame(error_data)
        fig = px.bar(df_errors, x='Module', y='Error Rate (%)', color='Error Rate (%)',
                    color_continuous_scale='Reds')
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    main()