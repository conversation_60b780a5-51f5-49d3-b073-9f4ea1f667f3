import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
import streamlit as st
from rdkit import Chem
from rdkit.Chem import Draw
import io
import base64

def create_molecule_visualization(smiles):
    """Create a 2D molecular structure visualization from SMILES"""
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None

        # Generate 2D coordinates
        from rdkit.Chem import rdDepictor
        rdDepictor.Compute2DCoords(mol)

        # Create image
        img = Draw.MolToImage(mol, size=(400, 400))

        # Convert to base64 for display
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"
    except Exception as e:
        st.error(f"Could not visualize molecule: {e}")
        return None

def create_survival_curve_plot(time_points, probabilities, title="Survival Curve"):
    """Create an interactive survival curve plot"""
    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=time_points,
        y=probabilities,
        mode='lines',
        name='Survival Probability',
        line=dict(color='#1f77b4', width=3),
        hovertemplate='<b>Time:</b> %{x} days<br><b>Survival Probability:</b> %{y:.3f}<extra></extra>'
    ))

    # Add confidence intervals (simulated)
    upper_ci = np.array(probabilities) + np.random.normal(0, 0.05, len(probabilities))
    lower_ci = np.array(probabilities) - np.random.normal(0, 0.05, len(probabilities))
    upper_ci = np.clip(upper_ci, 0, 1)
    lower_ci = np.clip(lower_ci, 0, 1)

    fig.add_trace(go.Scatter(
        x=time_points + time_points[::-1],
        y=list(upper_ci) + list(lower_ci[::-1]),
        fill='toself',
        fillcolor='rgba(31, 119, 180, 0.2)',
        line=dict(color='rgba(255,255,255,0)'),
        name='95% Confidence Interval',
        hoverinfo="skip"
    ))

    fig.update_layout(
        title=title,
        xaxis_title='Time (Days)',
        yaxis_title='Survival Probability',
        yaxis=dict(range=[0, 1]),
        hovermode='x unified',
        template='plotly_white'
    )

    return fig

def create_lifestyle_radar_chart(sleep_score, activity_score, nutrition_score, protein_score):
    """Create a radar chart for lifestyle factors"""
    categories = ['Sleep Quality', 'Physical Activity', 'Nutrition Balance', 'Protein Intake']
    values = [sleep_score, activity_score, nutrition_score, protein_score]

    # Add the first value at the end to close the radar chart
    values += values[:1]
    categories += categories[:1]

    fig = go.Figure()

    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        fillcolor='rgba(31, 119, 180, 0.3)',
        line=dict(color='#1f77b4', width=2),
        name='Your Profile'
    ))

    # Add reference lines
    fig.add_trace(go.Scatterpolar(
        r=[75, 75, 75, 75, 75],  # Target scores
        theta=categories,
        mode='lines',
        line=dict(color='green', width=2, dash='dash'),
        name='Target Level'
    ))

    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100],
                tickmode='linear',
                tick0=0,
                dtick=25
            )
        ),
        showlegend=True,
        title="Lifestyle Factor Analysis"
    )

    return fig

def create_emotion_distribution_plot(emotion_probs, emotion_labels):
    """Create a bar chart for emotion probability distribution"""
    colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#c2c2f0', '#ffb3e6', '#c4e17f']

    fig = go.Figure(data=[
        go.Bar(
            x=list(emotion_labels.values()),
            y=emotion_probs,
            marker_color=colors,
            hovertemplate='<b>%{x}</b><br>Probability: %{y:.3f}<extra></extra>'
        )
    ])

    fig.update_layout(
        title='Emotion Classification Probabilities',
        xaxis_title='Emotions',
        yaxis_title='Probability',
        template='plotly_white',
        showlegend=False
    )

    return fig

def create_audio_waveform_plot(audio_data, sample_rate, title="Audio Waveform"):
    """Create an audio waveform visualization"""
    time = np.linspace(0, len(audio_data) / sample_rate, len(audio_data))

    # Downsample for visualization if too long
    if len(audio_data) > 10000:
        step = len(audio_data) // 10000
        audio_data = audio_data[::step]
        time = time[::step]

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=time,
        y=audio_data,
        mode='lines',
        name='Amplitude',
        line=dict(color='#1f77b4', width=1),
        hovertemplate='<b>Time:</b> %{x:.3f}s<br><b>Amplitude:</b> %{y:.4f}<extra></extra>'
    ))

    fig.update_layout(
        title=title,
        xaxis_title='Time (seconds)',
        yaxis_title='Amplitude',
        template='plotly_white',
        hovermode='x unified'
    )

    return fig

def create_model_performance_comparison(modules, metrics):
    """Create a grouped bar chart comparing model performance across modules"""
    fig = go.Figure()

    metric_names = list(metrics.keys())
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

    for i, metric in enumerate(metric_names):
        fig.add_trace(go.Bar(
            name=metric,
            x=modules,
            y=metrics[metric],
            marker_color=colors[i % len(colors)],
            hovertemplate=f'<b>{metric}</b><br>Module: %{{x}}<br>Score: %{{y:.3f}}<extra></extra>'
        ))

    fig.update_layout(
        title='Model Performance Comparison',
        xaxis_title='Modules',
        yaxis_title='Score',
        barmode='group',
        template='plotly_white',
        yaxis=dict(range=[0.8, 1.0])
    )

    return fig

def create_usage_heatmap(usage_data, title="Usage Heatmap"):
    """Create a heatmap for usage patterns"""
    fig = px.imshow(
        usage_data,
        title=title,
        color_continuous_scale='Blues',
        aspect='auto'
    )

    fig.update_layout(
        xaxis_title='Hour of Day',
        yaxis_title='Day of Week'
    )

    return fig

def create_gauge_chart(value, title, max_value=100, color_ranges=None):
    """Create a gauge chart for metrics"""
    if color_ranges is None:
        color_ranges = [
            {'range': [0, 50], 'color': "lightgray"},
            {'range': [50, 80], 'color': "yellow"},
            {'range': [80, max_value], 'color': "green"}
        ]

    fig = go.Figure(go.Indicator(
        mode="gauge+number+delta",
        value=value,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': title},
        gauge={
            'axis': {'range': [None, max_value]},
            'bar': {'color': "darkblue"},
            'steps': color_ranges,
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': max_value * 0.9
            }
        }
    ))

    fig.update_layout(height=300)
    return fig

def create_time_series_plot(dates, values, title, y_label):
    """Create a time series plot"""
    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=dates,
        y=values,
        mode='lines+markers',
        name=y_label,
        line=dict(color='#1f77b4', width=2),
        marker=dict(size=4),
        hovertemplate=f'<b>Date:</b> %{{x}}<br><b>{y_label}:</b> %{{y}}<extra></extra>'
    ))

    # Add trend line
    z = np.polyfit(range(len(values)), values, 1)
    p = np.poly1d(z)
    trend_values = p(range(len(values)))

    fig.add_trace(go.Scatter(
        x=dates,
        y=trend_values,
        mode='lines',
        name='Trend',
        line=dict(color='red', width=2, dash='dash'),
        hoverinfo='skip'
    ))

    fig.update_layout(
        title=title,
        xaxis_title='Date',
        yaxis_title=y_label,
        template='plotly_white',
        hovermode='x unified'
    )

    return fig

def create_correlation_matrix(data, title="Correlation Matrix"):
    """Create a correlation matrix heatmap"""
    corr_matrix = data.corr()

    fig = px.imshow(
        corr_matrix,
        title=title,
        color_continuous_scale='RdBu',
        aspect='auto',
        zmin=-1,
        zmax=1
    )

    fig.update_layout(
        xaxis_title='Features',
        yaxis_title='Features'
    )

    return fig