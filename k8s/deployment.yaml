apiVersion: v1
kind: Namespace
metadata:
  name: lifemindml
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lifemindml-api
  namespace: lifemindml
  labels:
    app: lifemindml-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: lifemindml-api
  template:
    metadata:
      labels:
        app: lifemindml-api
    spec:
      containers:
      - name: lifemindml-api
        image: lifemindml:latest
        ports:
        - containerPort: 8000
        env:
        - name: PYTHONPATH
          value: "/home/<USER>"
        - name: LOG_LEVEL
          value: "INFO"
        - name: MODEL_PATH
          value: "/home/<USER>/models"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: models-volume
          mountPath: /home/<USER>/models
          readOnly: true
        - name: logs-volume
          mountPath: /home/<USER>/logs
      volumes:
      - name: models-volume
        persistentVolumeClaim:
          claimName: models-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: logs-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lifemindml-frontend
  namespace: lifemindml
  labels:
    app: lifemindml-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: lifemindml-frontend
  template:
    metadata:
      labels:
        app: lifemindml-frontend
    spec:
      containers:
      - name: lifemindml-frontend
        image: lifemindml:latest
        command: ["streamlit", "run", "app/streamlit_app.py", "--server.address", "0.0.0.0", "--server.port", "8501"]
        ports:
        - containerPort: 8501
        env:
        - name: PYTHONPATH
          value: "/home/<USER>"
        - name: API_BASE_URL
          value: "http://lifemindml-api-service:8000"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: models-volume
          mountPath: /home/<USER>/models
          readOnly: true
      volumes:
      - name: models-volume
        persistentVolumeClaim:
          claimName: models-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: lifemindml-api-service
  namespace: lifemindml
spec:
  selector:
    app: lifemindml-api
  ports:
  - protocol: TCP
    port: 8000
    targetPort: 8000
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: lifemindml-frontend-service
  namespace: lifemindml
spec:
  selector:
    app: lifemindml-frontend
  ports:
  - protocol: TCP
    port: 8501
    targetPort: 8501
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: lifemindml-ingress
  namespace: lifemindml
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
spec:
  tls:
  - hosts:
    - api.lifemindml.com
    - app.lifemindml.com
    secretName: lifemindml-tls
  rules:
  - host: api.lifemindml.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: lifemindml-api-service
            port:
              number: 8000
  - host: app.lifemindml.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: lifemindml-frontend-service
            port:
              number: 8501
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: models-pvc
  namespace: lifemindml
spec:
  accessModes:
    - ReadOnlyMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: logs-pvc
  namespace: lifemindml
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: lifemindml-api-pdb
  namespace: lifemindml
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: lifemindml-api
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: lifemindml-api-hpa
  namespace: lifemindml
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: lifemindml-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
