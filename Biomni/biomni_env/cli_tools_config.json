{"tools": [{"name": "PLINK 2.0", "function_name": "install_plink2", "description": "Whole genome association analysis toolset", "website": "https://www.cog-genomics.org/plink/2.0/", "downloads": {"linux": "https://s3.amazonaws.com/plink2-assets/alpha6/plink2_linux_avx2_20250129.zip", "macos_intel": "https://s3.amazonaws.com/plink2-assets/alpha6/plink2_mac_avx2_20250129.zip", "macos_arm64": "https://s3.amazonaws.com/plink2-assets/alpha6/plink2_mac_arm64_20250129.zip"}, "binary_path": "plink2", "version_command": "--version"}, {"name": "IQ-TREE", "function_name": "install_iqtree", "description": "Efficient phylogenetic software by maximum likelihood", "website": "http://www.iqtree.org/", "downloads": {"linux": "https://github.com/iqtree/iqtree2/releases/download/v*******/iqtree-*******-Linux.tar.gz", "macos_intel": "https://github.com/iqtree/iqtree2/releases/download/v*******/iqtree-*******-MacOSX.zip", "macos_arm64": "https://github.com/iqtree/iqtree2/releases/download/v*******/iqtree-*******-MacOSX.zip"}, "binary_path": "bin/iqtree2", "version_command": "--version"}, {"name": "GCTA", "function_name": "install_gcta", "description": "Genome-wide Complex Trait Analysis", "website": "https://yanglab.westlake.edu.cn/software/gcta/", "downloads": {"linux": "https://yanglab.westlake.edu.cn/software/gcta/bin/gcta-1.94.4-linux-kernel-3-x86_64.zip", "macos_intel": "https://yanglab.westlake.edu.cn/software/gcta/bin/gcta-1.94.1-macOS-x86_64.zip", "macos_arm64": "https://yanglab.westlake.edu.cn/software/gcta/bin/gcta-1.94.3-macOS-arm64.zip"}, "binary_path": "gcta64", "version_command": ""}, {"name": "BWA", "function_name": "install_bwa", "description": "Burr<PERSON>-<PERSON> for short-read alignment", "website": "https://github.com/lh3/bwa", "downloads": {"linux": "https://github.com/lh3/bwa.git", "macos_intel": "https://github.com/lh3/bwa.git", "macos_arm64": "https://github.com/lh3/bwa.git"}, "binary_path": "bwa", "version_command": ""}, {"name": "FastTree", "function_name": "install_fasttree", "description": "Approximately-Maximum-Likelihood phylogenetic trees from alignments", "website": "https://morgannprice.github.io/fasttree/", "downloads": {"linux": "https://morgannprice.github.io/fasttree/FastTree.c", "macos_intel": "https://morgannprice.github.io/fasttree/FastTree.c", "macos_arm64": "https://morgannprice.github.io/fasttree/FastTree.c"}, "binary_path": "FastTree", "version_command": "-help | head -n 2"}, {"name": "MUSCLE", "function_name": "install_muscle", "description": "Multiple sequence alignment with high accuracy and performance", "website": "https://github.com/rcedgar/muscle", "downloads": {"linux": "https://github.com/rcedgar/muscle/releases/download/v5.3/muscle-linux-x86.v5.3", "macos_arm64": "https://github.com/rcedgar/muscle/releases/download/v5.3/muscle-osx-arm64.v5.3"}, "binary_path": "muscle", "version_command": "-version"}, {"name": "HOMER", "function_name": "install_homer", "description": "Software for motif discovery and next-gen sequencing analysis", "website": "http://homer.ucsd.edu/homer/", "downloads": {"linux": "http://homer.ucsd.edu/homer/configureHomer.pl", "macos_intel": "http://homer.ucsd.edu/homer/configureHomer.pl", "macos_arm64": "http://homer.ucsd.edu/homer/configureHomer.pl"}, "binary_path": "bin/findMotifs.pl", "version_command": "-h"}]}