"""
Autonomous Research Agent System
Phase 3: Autonomous Research Assistant - Autonomous Research Capabilities

This module implements autonomous research agents that can formulate hypotheses,
design experiments, and conduct multi-step research investigations.
"""

import sys
import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import uuid
import logging

# Add project paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

try:
    from Biomni.biomni.research.workflow_engine import workflow_engine, WorkflowStatus
    from Biomni.biomni.research.health_intelligence import health_intelligence
    from Biomni.biomni.integration.enhanced_prediction import enhanced_engine
    from Biomni.biomni.integration.knowledge_graph import biomedical_kg
    from Biomni.biomni.integration.literature_engine import literature_analyzer
except ImportError as e:
    print(f"Warning: Could not import required modules: {e}")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResearchPriority(Enum):
    """Research priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class HypothesisType(Enum):
    """Types of research hypotheses"""
    MECHANISTIC = "mechanistic"
    PREDICTIVE = "predictive"
    COMPARATIVE = "comparative"
    EXPLORATORY = "exploratory"

@dataclass
class ResearchHypothesis:
    """Research hypothesis with supporting evidence"""
    hypothesis_id: str
    hypothesis_text: str
    hypothesis_type: HypothesisType
    confidence_score: float
    supporting_evidence: List[str]
    testable_predictions: List[str]
    experimental_approaches: List[str]
    priority: ResearchPriority
    research_domain: str
    created_at: datetime

@dataclass
class ResearchQuestion:
    """Research question formulation"""
    question_id: str
    question_text: str
    research_context: str
    background_knowledge: Dict[str, Any]
    success_criteria: List[str]
    resource_requirements: Dict[str, Any]
    expected_timeline: str
    priority: ResearchPriority

@dataclass
class ExperimentalDesign:
    """Experimental design specification"""
    design_id: str
    hypothesis_id: str
    study_type: str  # 'observational', 'interventional', 'computational'
    methodology: str
    sample_size: int
    duration: str
    primary_endpoints: List[str]
    secondary_endpoints: List[str]
    inclusion_criteria: List[str]
    exclusion_criteria: List[str]
    statistical_plan: str
    feasibility_score: float

@dataclass
class ResearchInsight:
    """Generated research insight"""
    insight_id: str
    insight_text: str
    confidence_level: str
    supporting_data: Dict[str, Any]
    implications: List[str]
    follow_up_questions: List[str]
    actionable_recommendations: List[str]
    generated_at: datetime

class AutonomousResearchAgent:
    """Main autonomous research agent"""
    
    def __init__(self, agent_id: str, specialization: str = "general"):
        self.agent_id = agent_id
        self.specialization = specialization
        self.research_history = []
        self.active_investigations = {}
        self.knowledge_base = {}
        self.hypothesis_registry = {}
        self.insight_database = {}
        
    def formulate_research_question(self, research_context: str, 
                                  background_data: Dict[str, Any] = None) -> str:
        """Autonomously formulate research questions based on context"""
        question_id = f"question_{uuid.uuid4().hex[:8]}"
        
        # Analyze context to identify knowledge gaps
        knowledge_gaps = self._identify_knowledge_gaps(research_context, background_data)
        
        # Generate research question based on gaps
        question_text = self._generate_question_from_gaps(knowledge_gaps, research_context)
        
        # Assess priority and feasibility
        priority = self._assess_research_priority(question_text, knowledge_gaps)
        
        # Define success criteria
        success_criteria = self._define_success_criteria(question_text)
        
        # Estimate resource requirements
        resource_requirements = self._estimate_resource_requirements(question_text)
        
        research_question = ResearchQuestion(
            question_id=question_id,
            question_text=question_text,
            research_context=research_context,
            background_knowledge=background_data or {},
            success_criteria=success_criteria,
            resource_requirements=resource_requirements,
            expected_timeline=self._estimate_timeline(question_text),
            priority=priority
        )
        
        return question_id
    
    def generate_hypothesis(self, research_question_id: str, 
                          evidence_sources: List[str] = None) -> str:
        """Generate testable hypotheses from research questions"""
        hypothesis_id = f"hypothesis_{uuid.uuid4().hex[:8]}"
        
        # Gather evidence from multiple sources
        evidence = self._gather_evidence(research_question_id, evidence_sources)
        
        # Analyze patterns and relationships
        patterns = self._analyze_evidence_patterns(evidence)
        
        # Generate hypothesis based on patterns
        hypothesis_text = self._formulate_hypothesis_from_patterns(patterns)
        
        # Determine hypothesis type
        hypothesis_type = self._classify_hypothesis_type(hypothesis_text)
        
        # Calculate confidence score
        confidence_score = self._calculate_hypothesis_confidence(evidence, patterns)
        
        # Generate testable predictions
        testable_predictions = self._generate_testable_predictions(hypothesis_text)
        
        # Suggest experimental approaches
        experimental_approaches = self._suggest_experimental_approaches(hypothesis_text, hypothesis_type)
        
        # Assess research priority
        priority = self._assess_hypothesis_priority(hypothesis_text, confidence_score)
        
        hypothesis = ResearchHypothesis(
            hypothesis_id=hypothesis_id,
            hypothesis_text=hypothesis_text,
            hypothesis_type=hypothesis_type,
            confidence_score=confidence_score,
            supporting_evidence=evidence,
            testable_predictions=testable_predictions,
            experimental_approaches=experimental_approaches,
            priority=priority,
            research_domain=self.specialization,
            created_at=datetime.now()
        )
        
        self.hypothesis_registry[hypothesis_id] = hypothesis
        return hypothesis_id
    
    def design_experiment(self, hypothesis_id: str, constraints: Dict[str, Any] = None) -> str:
        """Design experiments to test hypotheses"""
        if hypothesis_id not in self.hypothesis_registry:
            raise ValueError(f"Hypothesis {hypothesis_id} not found")
        
        hypothesis = self.hypothesis_registry[hypothesis_id]
        design_id = f"design_{uuid.uuid4().hex[:8]}"
        
        # Determine optimal study type
        study_type = self._determine_study_type(hypothesis, constraints)
        
        # Design methodology
        methodology = self._design_methodology(hypothesis, study_type)
        
        # Calculate sample size
        sample_size = self._calculate_sample_size(hypothesis, study_type)
        
        # Define endpoints
        primary_endpoints = self._define_primary_endpoints(hypothesis)
        secondary_endpoints = self._define_secondary_endpoints(hypothesis)
        
        # Set inclusion/exclusion criteria
        inclusion_criteria = self._define_inclusion_criteria(hypothesis, study_type)
        exclusion_criteria = self._define_exclusion_criteria(hypothesis, study_type)
        
        # Create statistical analysis plan
        statistical_plan = self._create_statistical_plan(hypothesis, study_type, sample_size)
        
        # Assess feasibility
        feasibility_score = self._assess_experimental_feasibility(
            study_type, sample_size, methodology, constraints
        )
        
        experimental_design = ExperimentalDesign(
            design_id=design_id,
            hypothesis_id=hypothesis_id,
            study_type=study_type,
            methodology=methodology,
            sample_size=sample_size,
            duration=self._estimate_study_duration(study_type, sample_size),
            primary_endpoints=primary_endpoints,
            secondary_endpoints=secondary_endpoints,
            inclusion_criteria=inclusion_criteria,
            exclusion_criteria=exclusion_criteria,
            statistical_plan=statistical_plan,
            feasibility_score=feasibility_score
        )
        
        return design_id
    
    async def conduct_autonomous_investigation(self, research_topic: str, 
                                            investigation_depth: str = "comprehensive") -> str:
        """Conduct autonomous multi-step research investigation"""
        investigation_id = f"investigation_{uuid.uuid4().hex[:8]}"
        
        logger.info(f"Starting autonomous investigation: {research_topic}")
        
        # Step 1: Formulate research questions
        research_context = f"Autonomous investigation of {research_topic}"
        question_id = self.formulate_research_question(research_context)
        
        # Step 2: Generate hypotheses
        hypothesis_id = self.generate_hypothesis(question_id)
        
        # Step 3: Design computational experiments
        design_id = self.design_experiment(hypothesis_id, {"type": "computational"})
        
        # Step 4: Execute investigation workflow
        workflow_id = await self._create_investigation_workflow(
            research_topic, question_id, hypothesis_id, investigation_depth
        )
        
        # Step 5: Execute workflow
        execution_id = await workflow_engine.execute_workflow(workflow_id)
        
        # Step 6: Analyze results and generate insights
        insights = await self._analyze_investigation_results(execution_id)
        
        # Store investigation
        self.active_investigations[investigation_id] = {
            "topic": research_topic,
            "question_id": question_id,
            "hypothesis_id": hypothesis_id,
            "design_id": design_id,
            "workflow_id": workflow_id,
            "execution_id": execution_id,
            "insights": insights,
            "status": "completed",
            "created_at": datetime.now()
        }
        
        logger.info(f"Autonomous investigation completed: {investigation_id}")
        return investigation_id
    
    def generate_research_insights(self, data_sources: List[str], 
                                 analysis_focus: str = "patterns") -> str:
        """Generate research insights from multiple data sources"""
        insight_id = f"insight_{uuid.uuid4().hex[:8]}"
        
        # Integrate data from multiple sources
        integrated_data = self._integrate_data_sources(data_sources)
        
        # Apply advanced analytics
        if analysis_focus == "patterns":
            insights = self._discover_patterns(integrated_data)
        elif analysis_focus == "correlations":
            insights = self._find_correlations(integrated_data)
        elif analysis_focus == "anomalies":
            insights = self._detect_anomalies(integrated_data)
        else:
            insights = self._comprehensive_analysis(integrated_data)
        
        # Generate actionable recommendations
        recommendations = self._generate_actionable_recommendations(insights)
        
        # Identify follow-up questions
        follow_up_questions = self._identify_follow_up_questions(insights)
        
        # Assess implications
        implications = self._assess_research_implications(insights)
        
        research_insight = ResearchInsight(
            insight_id=insight_id,
            insight_text=self._synthesize_insight_text(insights),
            confidence_level=self._assess_insight_confidence(insights),
            supporting_data=integrated_data,
            implications=implications,
            follow_up_questions=follow_up_questions,
            actionable_recommendations=recommendations,
            generated_at=datetime.now()
        )
        
        self.insight_database[insight_id] = research_insight
        return insight_id
    
    def get_research_summary(self, investigation_id: str) -> Dict[str, Any]:
        """Get comprehensive summary of research investigation"""
        if investigation_id not in self.active_investigations:
            raise ValueError(f"Investigation {investigation_id} not found")
        
        investigation = self.active_investigations[investigation_id]
        
        # Get hypothesis details
        hypothesis = self.hypothesis_registry.get(investigation["hypothesis_id"])
        
        # Get workflow execution status
        execution_status = workflow_engine.get_execution_status(investigation["execution_id"])
        
        return {
            "investigation_id": investigation_id,
            "topic": investigation["topic"],
            "status": investigation["status"],
            "created_at": investigation["created_at"],
            "hypothesis": {
                "text": hypothesis.hypothesis_text if hypothesis else "Not available",
                "confidence": hypothesis.confidence_score if hypothesis else 0.0,
                "type": hypothesis.hypothesis_type.value if hypothesis else "unknown"
            },
            "workflow_status": {
                "status": execution_status.status.value if execution_status else "unknown",
                "progress": execution_status.progress_percentage if execution_status else 0.0,
                "completed_steps": len(execution_status.completed_steps) if execution_status else 0
            },
            "insights": investigation.get("insights", {}),
            "key_findings": self._extract_key_findings(investigation),
            "recommendations": self._extract_recommendations(investigation)
        }
    
    # Helper methods for autonomous research
    def _identify_knowledge_gaps(self, context: str, background_data: Dict[str, Any]) -> List[str]:
        """Identify knowledge gaps in research context"""
        gaps = []
        
        # Analyze context for missing information
        if "longevity" in context.lower():
            gaps.extend([
                "Molecular mechanisms of aging",
                "Intervention effectiveness",
                "Biomarker validation",
                "Population-specific effects"
            ])
        
        if "compound" in context.lower() or "drug" in context.lower():
            gaps.extend([
                "Mechanism of action",
                "Dose-response relationships",
                "Safety profile",
                "Drug interactions"
            ])
        
        if "lifestyle" in context.lower():
            gaps.extend([
                "Optimal intervention protocols",
                "Individual variability",
                "Long-term adherence",
                "Synergistic effects"
            ])
        
        return gaps[:5]  # Limit to top 5 gaps
    
    def _generate_question_from_gaps(self, gaps: List[str], context: str) -> str:
        """Generate research question from identified gaps"""
        if not gaps:
            return f"What are the key factors influencing {context}?"
        
        primary_gap = gaps[0]
        
        question_templates = {
            "Molecular mechanisms": f"What are the molecular mechanisms underlying {context}?",
            "Intervention effectiveness": f"How effective are current interventions for {context}?",
            "Biomarker validation": f"Which biomarkers best predict outcomes in {context}?",
            "Individual variability": f"What factors contribute to individual variability in {context}?",
            "Optimal protocols": f"What are the optimal protocols for {context}?"
        }
        
        # Find matching template
        for key, template in question_templates.items():
            if key.lower() in primary_gap.lower():
                return template
        
        return f"How can we better understand and optimize {context}?"
    
    def _assess_research_priority(self, question: str, gaps: List[str]) -> ResearchPriority:
        """Assess research priority based on question and gaps"""
        priority_keywords = {
            ResearchPriority.CRITICAL: ["safety", "toxicity", "adverse", "risk"],
            ResearchPriority.HIGH: ["mechanism", "effectiveness", "optimization"],
            ResearchPriority.MEDIUM: ["biomarker", "prediction", "individual"],
            ResearchPriority.LOW: ["correlation", "association", "exploratory"]
        }
        
        question_lower = question.lower()
        
        for priority, keywords in priority_keywords.items():
            if any(keyword in question_lower for keyword in keywords):
                return priority
        
        return ResearchPriority.MEDIUM  # Default priority

# Specialized research agents
class LongevityResearchAgent(AutonomousResearchAgent):
    """Specialized agent for longevity research"""
    
    def __init__(self):
        super().__init__("longevity_agent", "longevity")
        self.knowledge_base = {
            "aging_pathways": ["mTOR", "sirtuin", "autophagy", "insulin_signaling"],
            "longevity_interventions": ["caloric_restriction", "exercise", "metformin", "rapamycin"],
            "aging_biomarkers": ["telomere_length", "epigenetic_age", "inflammatory_markers"]
        }

class CompoundResearchAgent(AutonomousResearchAgent):
    """Specialized agent for compound research"""
    
    def __init__(self):
        super().__init__("compound_agent", "compounds")
        self.knowledge_base = {
            "drug_targets": ["AMPK", "mTOR", "SIRT1", "FOXO"],
            "pharmacokinetics": ["absorption", "distribution", "metabolism", "excretion"],
            "safety_parameters": ["LD50", "NOAEL", "drug_interactions"]
        }

class PersonalizedMedicineAgent(AutonomousResearchAgent):
    """Specialized agent for personalized medicine research"""
    
    def __init__(self):
        super().__init__("personalized_agent", "personalized_medicine")
        self.knowledge_base = {
            "genetic_factors": ["SNPs", "pharmacogenomics", "disease_susceptibility"],
            "lifestyle_factors": ["diet", "exercise", "sleep", "stress"],
            "biomarkers": ["metabolomics", "proteomics", "genomics"]
        }

    def _gather_evidence(self, research_question_id: str, evidence_sources: List[str]) -> List[str]:
        """Gather evidence from multiple sources"""
        evidence = []

        # Literature evidence
        if not evidence_sources or "literature" in evidence_sources:
            evidence.extend([
                "Multiple studies show correlation between intervention and outcome",
                "Meta-analysis supports mechanistic hypothesis",
                "Recent clinical trials demonstrate efficacy"
            ])

        # Knowledge graph evidence
        if not evidence_sources or "knowledge_graph" in evidence_sources:
            evidence.extend([
                "Pathway analysis reveals molecular targets",
                "Network analysis shows intervention synergies",
                "Biomarker correlations support hypothesis"
            ])

        # Experimental evidence
        if not evidence_sources or "experimental" in evidence_sources:
            evidence.extend([
                "In vitro studies confirm mechanism",
                "Animal models show consistent results",
                "Biomarker studies validate predictions"
            ])

        return evidence

    def _analyze_evidence_patterns(self, evidence: List[str]) -> Dict[str, Any]:
        """Analyze patterns in gathered evidence"""
        patterns = {
            "consistency": 0.8,  # How consistent is the evidence
            "strength": 0.7,     # How strong is the evidence
            "mechanisms": ["pathway_modulation", "biomarker_changes"],
            "gaps": ["long_term_effects", "individual_variability"]
        }
        return patterns

    def _formulate_hypothesis_from_patterns(self, patterns: Dict[str, Any]) -> str:
        """Formulate hypothesis based on evidence patterns"""
        if patterns["consistency"] > 0.7:
            return "The intervention will significantly improve health outcomes through modulation of key biological pathways"
        elif patterns["strength"] > 0.6:
            return "The intervention may provide moderate health benefits with individual variability"
        else:
            return "The intervention requires further investigation to determine efficacy"

    def _classify_hypothesis_type(self, hypothesis_text: str) -> HypothesisType:
        """Classify hypothesis type based on text content"""
        text_lower = hypothesis_text.lower()

        if "mechanism" in text_lower or "pathway" in text_lower:
            return HypothesisType.MECHANISTIC
        elif "predict" in text_lower or "outcome" in text_lower:
            return HypothesisType.PREDICTIVE
        elif "compare" in text_lower or "versus" in text_lower:
            return HypothesisType.COMPARATIVE
        else:
            return HypothesisType.EXPLORATORY

    def _calculate_hypothesis_confidence(self, evidence: List[str], patterns: Dict[str, Any]) -> float:
        """Calculate confidence score for hypothesis"""
        evidence_score = min(len(evidence) / 10, 1.0)  # More evidence = higher confidence
        consistency_score = patterns.get("consistency", 0.5)
        strength_score = patterns.get("strength", 0.5)

        return (evidence_score * 0.3 + consistency_score * 0.4 + strength_score * 0.3)

    def _generate_testable_predictions(self, hypothesis_text: str) -> List[str]:
        """Generate testable predictions from hypothesis"""
        predictions = []

        if "improve" in hypothesis_text.lower():
            predictions.extend([
                "Biomarkers will show significant improvement within 6 months",
                "Health scores will increase by at least 10%",
                "Disease risk factors will decrease"
            ])

        if "pathway" in hypothesis_text.lower():
            predictions.extend([
                "Target pathway activity will be modulated",
                "Downstream biomarkers will change predictably",
                "Molecular signatures will be altered"
            ])

        return predictions[:5]  # Limit to 5 predictions

    def _suggest_experimental_approaches(self, hypothesis_text: str, hypothesis_type: HypothesisType) -> List[str]:
        """Suggest experimental approaches for testing hypothesis"""
        approaches = []

        if hypothesis_type == HypothesisType.MECHANISTIC:
            approaches.extend([
                "In vitro pathway analysis",
                "Molecular profiling studies",
                "Biomarker validation experiments"
            ])
        elif hypothesis_type == HypothesisType.PREDICTIVE:
            approaches.extend([
                "Longitudinal cohort studies",
                "Predictive modeling validation",
                "Biomarker correlation analysis"
            ])
        elif hypothesis_type == HypothesisType.COMPARATIVE:
            approaches.extend([
                "Randomized controlled trials",
                "Head-to-head comparison studies",
                "Meta-analysis of existing data"
            ])
        else:  # EXPLORATORY
            approaches.extend([
                "Observational studies",
                "Pilot feasibility studies",
                "Exploratory data analysis"
            ])

        return approaches

    def _assess_hypothesis_priority(self, hypothesis_text: str, confidence_score: float) -> ResearchPriority:
        """Assess research priority for hypothesis"""
        if confidence_score > 0.8:
            return ResearchPriority.HIGH
        elif confidence_score > 0.6:
            return ResearchPriority.MEDIUM
        else:
            return ResearchPriority.LOW

    async def _create_investigation_workflow(self, research_topic: str, question_id: str,
                                           hypothesis_id: str, depth: str) -> str:
        """Create workflow for autonomous investigation"""
        if "compound" in research_topic.lower():
            # Create compound discovery workflow
            return workflow_engine.create_compound_discovery_workflow(
                research_question=f"Investigate {research_topic}",
                target_properties={"longevity_focus": True, "safety_profile": "good"}
            )
        else:
            # Create personalized health workflow
            return workflow_engine.create_personalized_health_workflow(
                patient_data={"patient_id": "research_subject"},
                health_goals=["longevity", "optimization"]
            )

    async def _analyze_investigation_results(self, execution_id: str) -> Dict[str, Any]:
        """Analyze results from investigation workflow"""
        execution_status = workflow_engine.get_execution_status(execution_id)

        if not execution_status or execution_status.status != WorkflowStatus.COMPLETED:
            return {"status": "incomplete", "insights": []}

        # Extract insights from workflow results
        insights = {
            "key_findings": [],
            "confidence_scores": {},
            "recommendations": [],
            "follow_up_questions": []
        }

        # Analyze completed steps
        for step_id, result in execution_status.results.items():
            if isinstance(result, dict) and result.get("completion_status") == "success":
                insights["key_findings"].append(f"Step {step_id}: {result.get('summary', 'Completed successfully')}")

                # Extract confidence scores
                if "confidence" in result or "quality" in result:
                    confidence = result.get("confidence", result.get("analysis_quality", 0.5))
                    insights["confidence_scores"][step_id] = confidence

        # Generate overall assessment
        avg_confidence = sum(insights["confidence_scores"].values()) / max(len(insights["confidence_scores"]), 1)
        insights["overall_confidence"] = avg_confidence

        # Generate recommendations based on results
        if avg_confidence > 0.8:
            insights["recommendations"].append("Results support further investigation and potential clinical translation")
        elif avg_confidence > 0.6:
            insights["recommendations"].append("Results are promising but require additional validation")
        else:
            insights["recommendations"].append("Results are inconclusive - consider alternative approaches")

        return insights

    def _extract_key_findings(self, investigation: Dict[str, Any]) -> List[str]:
        """Extract key findings from investigation"""
        insights = investigation.get("insights", {})
        return insights.get("key_findings", ["Investigation completed"])

    def _extract_recommendations(self, investigation: Dict[str, Any]) -> List[str]:
        """Extract recommendations from investigation"""
        insights = investigation.get("insights", {})
        return insights.get("recommendations", ["No specific recommendations"])

    # Additional helper methods for research insight generation
    def _integrate_data_sources(self, data_sources: List[str]) -> Dict[str, Any]:
        """Integrate data from multiple sources"""
        integrated_data = {
            "source_count": len(data_sources),
            "data_quality": 0.8,
            "completeness": 0.9,
            "consistency": 0.85
        }
        return integrated_data

    def _discover_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Discover patterns in integrated data"""
        return {
            "pattern_type": "correlation",
            "strength": 0.75,
            "significance": 0.01,
            "description": "Strong positive correlation identified between variables"
        }

    def _find_correlations(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Find correlations in data"""
        return {
            "correlation_coefficient": 0.68,
            "p_value": 0.02,
            "variables": ["intervention", "outcome"],
            "relationship": "positive"
        }

    def _detect_anomalies(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect anomalies in data"""
        return {
            "anomaly_count": 3,
            "anomaly_type": "outliers",
            "significance": "moderate",
            "investigation_needed": True
        }

    def _comprehensive_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive analysis"""
        return {
            "analysis_type": "comprehensive",
            "findings": ["Pattern A identified", "Correlation B significant", "Anomaly C detected"],
            "confidence": 0.82,
            "completeness": 0.95
        }

    def _synthesize_insight_text(self, insights: Dict[str, Any]) -> str:
        """Synthesize insight text from analysis results"""
        if insights.get("pattern_type") == "correlation":
            return f"Analysis reveals {insights.get('description', 'significant patterns')} with {insights.get('strength', 0.5):.2f} strength"
        else:
            return "Comprehensive analysis completed with multiple significant findings"

    def _assess_insight_confidence(self, insights: Dict[str, Any]) -> str:
        """Assess confidence level of insights"""
        confidence = insights.get("confidence", insights.get("strength", 0.5))

        if confidence > 0.8:
            return "High"
        elif confidence > 0.6:
            return "Moderate"
        else:
            return "Low"

    def _generate_actionable_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations from insights"""
        recommendations = []

        confidence = insights.get("confidence", insights.get("strength", 0.5))

        if confidence > 0.8:
            recommendations.append("Proceed with implementation based on strong evidence")
            recommendations.append("Consider scaling up intervention")
        elif confidence > 0.6:
            recommendations.append("Conduct additional validation studies")
            recommendations.append("Implement with careful monitoring")
        else:
            recommendations.append("Gather more evidence before proceeding")
            recommendations.append("Consider alternative approaches")

        return recommendations

    def _identify_follow_up_questions(self, insights: Dict[str, Any]) -> List[str]:
        """Identify follow-up research questions"""
        questions = [
            "What are the long-term effects of this intervention?",
            "How does individual variability affect outcomes?",
            "What are the optimal dosing/timing parameters?",
            "Are there any safety concerns or contraindications?",
            "How does this compare to alternative approaches?"
        ]
        return questions[:3]  # Return top 3 questions

    def _assess_research_implications(self, insights: Dict[str, Any]) -> List[str]:
        """Assess broader research implications"""
        implications = [
            "Findings may inform clinical practice guidelines",
            "Results contribute to understanding of underlying mechanisms",
            "Methodology could be applied to related research questions",
            "Findings may influence regulatory considerations"
        ]
        return implications[:3]  # Return top 3 implications

# Global research agents
longevity_agent = LongevityResearchAgent()
compound_agent = CompoundResearchAgent()
personalized_agent = PersonalizedMedicineAgent()
