"""
Personalized Health Intelligence System
Phase 3: Autonomous Research Assistant - Personalized Health Intelligence

This module implements personalized health intelligence with individual risk profiling,
intervention recommendations, and longitudinal monitoring capabilities.
"""

import sys
import os
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import uuid
import numpy as np
import pandas as pd
from collections import defaultdict

# Add project paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

try:
    from Biomni.biomni.integration.enhanced_prediction import enhanced_engine
    from Biomni.biomni.integration.knowledge_graph import biomedical_kg
    from Biomni.biomni.research.workflow_engine import workflow_engine
except ImportError as e:
    print(f"Warning: Could not import required modules: {e}")

@dataclass
class HealthProfile:
    """Comprehensive health profile for an individual"""
    profile_id: str
    user_id: str
    created_at: datetime
    updated_at: datetime
    
    # Basic demographics
    age: int
    sex: str  # 'M' or 'F'
    height_cm: float
    weight_kg: float
    
    # Lifestyle factors
    lifestyle_data: Dict[str, Any]
    
    # Clinical data
    clinical_data: Dict[str, Any]
    
    # Genetic data (if available)
    genetic_data: Optional[Dict[str, Any]] = None
    
    # Health goals
    health_goals: List[str] = None
    
    # Risk preferences
    risk_tolerance: str = "moderate"  # 'conservative', 'moderate', 'aggressive'

@dataclass
class HealthAssessment:
    """Health assessment results"""
    assessment_id: str
    profile_id: str
    assessment_date: datetime
    
    # Risk scores
    disease_risks: Dict[str, float]
    overall_risk_score: float
    
    # Biological age
    biological_age: float
    age_acceleration: float  # biological_age - chronological_age
    
    # Biomarker predictions
    biomarker_predictions: Dict[str, Any]
    
    # Health score (0-100)
    health_score: float
    
    # Assessment confidence
    confidence_score: float

@dataclass
class PersonalizedIntervention:
    """Personalized intervention recommendation"""
    intervention_id: str
    intervention_type: str  # 'lifestyle', 'nutritional', 'supplemental', 'medical'
    name: str
    description: str
    
    # Personalization factors
    personalization_score: float
    evidence_strength: str
    expected_benefit: float
    
    # Implementation details
    dosage_or_frequency: str
    duration: str
    monitoring_parameters: List[str]
    
    # Contraindications and considerations
    contraindications: List[str]
    side_effects: List[str]
    interactions: List[str]
    
    # Cost and accessibility
    estimated_cost: Optional[float] = None
    accessibility_score: float = 1.0

@dataclass
class HealthTrajectory:
    """Predicted health trajectory"""
    trajectory_id: str
    profile_id: str
    prediction_date: datetime
    
    # Time horizons
    short_term_months: int = 6
    medium_term_years: int = 2
    long_term_years: int = 10
    
    # Predicted outcomes
    predicted_biological_age: Dict[str, float]  # time_horizon -> predicted_age
    predicted_disease_risks: Dict[str, Dict[str, float]]  # time_horizon -> {disease: risk}
    predicted_biomarkers: Dict[str, Dict[str, Any]]  # time_horizon -> biomarker_values
    
    # Intervention impact
    intervention_benefits: Dict[str, float]  # intervention_id -> expected_benefit
    
    # Confidence intervals
    confidence_intervals: Dict[str, Tuple[float, float]]

class PersonalizedHealthIntelligence:
    """Main system for personalized health intelligence"""
    
    def __init__(self):
        self.health_profiles = {}  # profile_id -> HealthProfile
        self.assessments = defaultdict(list)  # profile_id -> List[HealthAssessment]
        self.interventions = defaultdict(list)  # profile_id -> List[PersonalizedIntervention]
        self.trajectories = defaultdict(list)  # profile_id -> List[HealthTrajectory]
        
    def create_health_profile(self, user_id: str, demographic_data: Dict[str, Any],
                            lifestyle_data: Dict[str, Any], clinical_data: Dict[str, Any],
                            health_goals: List[str] = None, genetic_data: Dict[str, Any] = None) -> str:
        """Create a comprehensive health profile"""
        profile_id = f"profile_{uuid.uuid4().hex[:8]}"
        
        profile = HealthProfile(
            profile_id=profile_id,
            user_id=user_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            age=demographic_data.get("age", 30),
            sex=demographic_data.get("sex", "M"),
            height_cm=demographic_data.get("height_cm", 170),
            weight_kg=demographic_data.get("weight_kg", 70),
            lifestyle_data=lifestyle_data,
            clinical_data=clinical_data,
            genetic_data=genetic_data,
            health_goals=health_goals or ["longevity", "disease_prevention"],
            risk_tolerance=demographic_data.get("risk_tolerance", "moderate")
        )
        
        self.health_profiles[profile_id] = profile
        return profile_id
    
    def perform_comprehensive_assessment(self, profile_id: str) -> str:
        """Perform comprehensive health assessment"""
        if profile_id not in self.health_profiles:
            raise ValueError(f"Health profile {profile_id} not found")
        
        profile = self.health_profiles[profile_id]
        assessment_id = f"assessment_{uuid.uuid4().hex[:8]}"
        
        # Perform multi-modal analysis using enhanced engine
        patient_data = {
            "patient_id": profile.user_id,
            "lifestyle": profile.lifestyle_data,
            "clinical": profile.clinical_data
        }
        
        # Get enhanced analysis
        analysis_results = enhanced_engine.multi_modal_health_analysis(patient_data)
        
        # Extract key metrics
        disease_risks = {}
        biological_age = profile.age  # Default
        health_score = 70.0  # Default
        confidence_score = 0.7  # Default
        
        # Process lifestyle analysis
        if 'lifestyle' in analysis_results:
            lifestyle_analysis = analysis_results['lifestyle']
            biological_age = lifestyle_analysis.original_prediction
            confidence_score = lifestyle_analysis.combined_confidence
            
            # Extract disease risks from knowledge graph insights
            if hasattr(lifestyle_analysis, 'knowledge_graph_insights'):
                kg_insights = lifestyle_analysis.knowledge_graph_insights
                if 'disease_risks' in kg_insights:
                    disease_risks = kg_insights['disease_risks']
        
        # Calculate health score based on multiple factors
        age_factor = max(0, 1 - abs(biological_age - profile.age) / 20)  # Penalty for age acceleration
        risk_factor = 1 - (sum(disease_risks.values()) / max(len(disease_risks), 1))  # Lower risk = higher score
        health_score = (age_factor * 0.4 + risk_factor * 0.6) * 100
        
        # Predict biomarkers
        biomarker_predictions = self._predict_biomarkers(profile, disease_risks)
        
        # Create assessment
        assessment = HealthAssessment(
            assessment_id=assessment_id,
            profile_id=profile_id,
            assessment_date=datetime.now(),
            disease_risks=disease_risks,
            overall_risk_score=sum(disease_risks.values()) / max(len(disease_risks), 1),
            biological_age=biological_age,
            age_acceleration=biological_age - profile.age,
            biomarker_predictions=biomarker_predictions,
            health_score=health_score,
            confidence_score=confidence_score
        )
        
        self.assessments[profile_id].append(assessment)
        return assessment_id
    
    def generate_personalized_interventions(self, profile_id: str, 
                                          assessment_id: str = None) -> List[str]:
        """Generate personalized intervention recommendations"""
        if profile_id not in self.health_profiles:
            raise ValueError(f"Health profile {profile_id} not found")
        
        profile = self.health_profiles[profile_id]
        
        # Get latest assessment if not specified
        if assessment_id is None:
            if not self.assessments[profile_id]:
                raise ValueError("No assessments found. Perform assessment first.")
            assessment = self.assessments[profile_id][-1]
        else:
            assessment = next((a for a in self.assessments[profile_id] 
                             if a.assessment_id == assessment_id), None)
            if not assessment:
                raise ValueError(f"Assessment {assessment_id} not found")
        
        # Create workflow for personalized intervention analysis
        workflow_id = workflow_engine.create_personalized_health_workflow(
            patient_data={
                "patient_id": profile.user_id,
                "lifestyle": profile.lifestyle_data,
                "clinical": profile.clinical_data
            },
            health_goals=profile.health_goals
        )
        
        # Execute workflow (simplified for demo)
        intervention_ids = []
        
        # Generate lifestyle interventions
        lifestyle_interventions = self._generate_lifestyle_interventions(profile, assessment)
        intervention_ids.extend(lifestyle_interventions)
        
        # Generate nutritional interventions
        nutritional_interventions = self._generate_nutritional_interventions(profile, assessment)
        intervention_ids.extend(nutritional_interventions)
        
        # Generate supplemental interventions
        supplemental_interventions = self._generate_supplemental_interventions(profile, assessment)
        intervention_ids.extend(supplemental_interventions)
        
        return intervention_ids
    
    def predict_health_trajectory(self, profile_id: str, intervention_ids: List[str] = None) -> str:
        """Predict health trajectory with and without interventions"""
        if profile_id not in self.health_profiles:
            raise ValueError(f"Health profile {profile_id} not found")
        
        profile = self.health_profiles[profile_id]
        trajectory_id = f"trajectory_{uuid.uuid4().hex[:8]}"
        
        # Get latest assessment
        if not self.assessments[profile_id]:
            raise ValueError("No assessments found. Perform assessment first.")
        
        latest_assessment = self.assessments[profile_id][-1]
        
        # Predict trajectory without interventions (baseline)
        baseline_trajectory = self._predict_baseline_trajectory(profile, latest_assessment)
        
        # Predict trajectory with interventions
        intervention_benefits = {}
        if intervention_ids:
            for intervention_id in intervention_ids:
                intervention = next((i for i in self.interventions[profile_id] 
                                  if i.intervention_id == intervention_id), None)
                if intervention:
                    benefit = self._calculate_intervention_benefit(intervention, latest_assessment)
                    intervention_benefits[intervention_id] = benefit
        
        # Apply intervention benefits to baseline trajectory
        enhanced_trajectory = self._apply_intervention_benefits(
            baseline_trajectory, intervention_benefits
        )
        
        # Calculate confidence intervals
        confidence_intervals = self._calculate_trajectory_confidence(
            baseline_trajectory, enhanced_trajectory
        )
        
        trajectory = HealthTrajectory(
            trajectory_id=trajectory_id,
            profile_id=profile_id,
            prediction_date=datetime.now(),
            predicted_biological_age=enhanced_trajectory["biological_age"],
            predicted_disease_risks=enhanced_trajectory["disease_risks"],
            predicted_biomarkers=enhanced_trajectory["biomarkers"],
            intervention_benefits=intervention_benefits,
            confidence_intervals=confidence_intervals
        )
        
        self.trajectories[profile_id].append(trajectory)
        return trajectory_id
    
    def get_health_insights(self, profile_id: str) -> Dict[str, Any]:
        """Get comprehensive health insights for a profile"""
        if profile_id not in self.health_profiles:
            raise ValueError(f"Health profile {profile_id} not found")
        
        profile = self.health_profiles[profile_id]
        assessments = self.assessments[profile_id]
        interventions = self.interventions[profile_id]
        trajectories = self.trajectories[profile_id]
        
        # Latest assessment
        latest_assessment = assessments[-1] if assessments else None
        
        # Health trends
        health_trends = self._calculate_health_trends(assessments)
        
        # Risk prioritization
        risk_priorities = self._prioritize_risks(latest_assessment) if latest_assessment else {}
        
        # Intervention effectiveness
        intervention_effectiveness = self._assess_intervention_effectiveness(interventions, trajectories)
        
        # Personalized recommendations
        recommendations = self._generate_personalized_recommendations(
            profile, latest_assessment, risk_priorities
        )
        
        return {
            "profile_summary": {
                "profile_id": profile_id,
                "age": profile.age,
                "biological_age": latest_assessment.biological_age if latest_assessment else None,
                "health_score": latest_assessment.health_score if latest_assessment else None,
                "last_assessment": latest_assessment.assessment_date if latest_assessment else None
            },
            "health_trends": health_trends,
            "risk_priorities": risk_priorities,
            "intervention_effectiveness": intervention_effectiveness,
            "personalized_recommendations": recommendations,
            "trajectory_summary": self._summarize_trajectories(trajectories)
        }
    
    def _predict_biomarkers(self, profile: HealthProfile, disease_risks: Dict[str, float]) -> Dict[str, Any]:
        """Predict biomarker values based on risk assessment"""
        biomarkers = {}
        
        # Cardiovascular biomarkers
        cv_risk = disease_risks.get("cardiovascular_disease", 0.5)
        biomarkers["cardiovascular"] = {
            "total_cholesterol": 180 + (cv_risk * 60),
            "ldl_cholesterol": 100 + (cv_risk * 50),
            "hdl_cholesterol": 60 - (cv_risk * 20),
            "triglycerides": 100 + (cv_risk * 100),
            "crp": 1.0 + (cv_risk * 3.0)
        }
        
        # Metabolic biomarkers
        met_risk = disease_risks.get("diabetes_type2", 0.5)
        biomarkers["metabolic"] = {
            "glucose": 90 + (met_risk * 30),
            "hba1c": 5.0 + (met_risk * 1.5),
            "insulin": 10 + (met_risk * 15)
        }
        
        return biomarkers
    
    def _generate_lifestyle_interventions(self, profile: HealthProfile, 
                                        assessment: HealthAssessment) -> List[str]:
        """Generate personalized lifestyle interventions"""
        intervention_ids = []
        
        # Exercise interventions
        if assessment.disease_risks.get("cardiovascular_disease", 0) > 0.6:
            intervention = PersonalizedIntervention(
                intervention_id=f"lifestyle_exercise_{uuid.uuid4().hex[:6]}",
                intervention_type="lifestyle",
                name="Cardiovascular Exercise Program",
                description="Structured aerobic exercise program for cardiovascular health",
                personalization_score=0.85,
                evidence_strength="Strong",
                expected_benefit=0.25,
                dosage_or_frequency="150 minutes moderate intensity per week",
                duration="Ongoing",
                monitoring_parameters=["heart_rate", "blood_pressure", "exercise_capacity"],
                contraindications=["severe_heart_disease", "uncontrolled_hypertension"],
                side_effects=["muscle_soreness", "fatigue"],
                interactions=[],
                estimated_cost=50.0,
                accessibility_score=0.9
            )
            self.interventions[profile.profile_id].append(intervention)
            intervention_ids.append(intervention.intervention_id)
        
        # Sleep optimization
        if profile.lifestyle_data.get("sleep_hours", 8) < 7:
            intervention = PersonalizedIntervention(
                intervention_id=f"lifestyle_sleep_{uuid.uuid4().hex[:6]}",
                intervention_type="lifestyle",
                name="Sleep Optimization Program",
                description="Comprehensive sleep hygiene and optimization protocol",
                personalization_score=0.80,
                evidence_strength="Strong",
                expected_benefit=0.20,
                dosage_or_frequency="7-9 hours nightly",
                duration="Ongoing",
                monitoring_parameters=["sleep_duration", "sleep_quality", "sleep_efficiency"],
                contraindications=["sleep_disorders"],
                side_effects=[],
                interactions=[],
                estimated_cost=0.0,
                accessibility_score=1.0
            )
            self.interventions[profile.profile_id].append(intervention)
            intervention_ids.append(intervention.intervention_id)
        
        return intervention_ids
    
    def _generate_nutritional_interventions(self, profile: HealthProfile, 
                                          assessment: HealthAssessment) -> List[str]:
        """Generate personalized nutritional interventions"""
        intervention_ids = []
        
        # Mediterranean diet for cardiovascular health
        if assessment.disease_risks.get("cardiovascular_disease", 0) > 0.5:
            intervention = PersonalizedIntervention(
                intervention_id=f"nutrition_mediterranean_{uuid.uuid4().hex[:6]}",
                intervention_type="nutritional",
                name="Mediterranean Diet Protocol",
                description="Evidence-based Mediterranean diet for cardiovascular health",
                personalization_score=0.82,
                evidence_strength="Very Strong",
                expected_benefit=0.30,
                dosage_or_frequency="Daily meal planning",
                duration="Ongoing",
                monitoring_parameters=["lipid_profile", "inflammation_markers", "weight"],
                contraindications=["food_allergies"],
                side_effects=[],
                interactions=[],
                estimated_cost=100.0,
                accessibility_score=0.8
            )
            self.interventions[profile.profile_id].append(intervention)
            intervention_ids.append(intervention.intervention_id)
        
        return intervention_ids
    
    def _generate_supplemental_interventions(self, profile: HealthProfile, 
                                           assessment: HealthAssessment) -> List[str]:
        """Generate personalized supplemental interventions"""
        intervention_ids = []
        
        # Omega-3 for cardiovascular health
        if assessment.disease_risks.get("cardiovascular_disease", 0) > 0.5:
            intervention = PersonalizedIntervention(
                intervention_id=f"supplement_omega3_{uuid.uuid4().hex[:6]}",
                intervention_type="supplemental",
                name="Omega-3 Fatty Acids",
                description="High-quality omega-3 supplementation for cardiovascular protection",
                personalization_score=0.75,
                evidence_strength="Strong",
                expected_benefit=0.18,
                dosage_or_frequency="2g EPA+DHA daily",
                duration="Ongoing",
                monitoring_parameters=["lipid_profile", "inflammation_markers"],
                contraindications=["bleeding_disorders", "fish_allergy"],
                side_effects=["gastrointestinal_upset", "fishy_taste"],
                interactions=["anticoagulants"],
                estimated_cost=30.0,
                accessibility_score=0.95
            )
            self.interventions[profile.profile_id].append(intervention)
            intervention_ids.append(intervention.intervention_id)
        
        return intervention_ids

    def _predict_baseline_trajectory(self, profile: HealthProfile,
                                   assessment: HealthAssessment) -> Dict[str, Any]:
        """Predict baseline health trajectory without interventions"""
        current_age = profile.age
        current_bio_age = assessment.biological_age
        current_risks = assessment.disease_risks

        # Predict biological age progression
        age_acceleration_rate = 0.02  # 2% additional aging per year
        predicted_bio_age = {
            "6_months": current_bio_age + 0.5 * (1 + age_acceleration_rate),
            "2_years": current_bio_age + 2 * (1 + age_acceleration_rate),
            "10_years": current_bio_age + 10 * (1 + age_acceleration_rate)
        }

        # Predict disease risk progression
        risk_progression_rate = 0.05  # 5% increase per year
        predicted_risks = {}
        for horizon in ["6_months", "2_years", "10_years"]:
            years = {"6_months": 0.5, "2_years": 2, "10_years": 10}[horizon]
            predicted_risks[horizon] = {}
            for disease, current_risk in current_risks.items():
                progression = current_risk * (1 + risk_progression_rate * years)
                predicted_risks[horizon][disease] = min(progression, 1.0)

        # Predict biomarker changes
        predicted_biomarkers = {}
        for horizon in ["6_months", "2_years", "10_years"]:
            predicted_biomarkers[horizon] = assessment.biomarker_predictions.copy()
            # Apply gradual deterioration
            years = {"6_months": 0.5, "2_years": 2, "10_years": 10}[horizon]
            deterioration_factor = 1 + (0.02 * years)  # 2% deterioration per year

            for category, biomarkers in predicted_biomarkers[horizon].items():
                for biomarker, value in biomarkers.items():
                    if biomarker in ["total_cholesterol", "ldl_cholesterol", "glucose", "crp"]:
                        predicted_biomarkers[horizon][category][biomarker] = value * deterioration_factor
                    elif biomarker in ["hdl_cholesterol"]:
                        predicted_biomarkers[horizon][category][biomarker] = value / deterioration_factor

        return {
            "biological_age": predicted_bio_age,
            "disease_risks": predicted_risks,
            "biomarkers": predicted_biomarkers
        }

    def _calculate_intervention_benefit(self, intervention: PersonalizedIntervention,
                                      assessment: HealthAssessment) -> float:
        """Calculate expected benefit from an intervention"""
        base_benefit = intervention.expected_benefit

        # Adjust based on current risk level
        relevant_risks = []
        if intervention.intervention_type == "lifestyle" and "exercise" in intervention.name.lower():
            relevant_risks.append(assessment.disease_risks.get("cardiovascular_disease", 0.5))
        elif "cardiovascular" in intervention.description.lower():
            relevant_risks.append(assessment.disease_risks.get("cardiovascular_disease", 0.5))
        elif "metabolic" in intervention.description.lower():
            relevant_risks.append(assessment.disease_risks.get("diabetes_type2", 0.5))

        if relevant_risks:
            avg_risk = sum(relevant_risks) / len(relevant_risks)
            # Higher risk = higher potential benefit
            risk_multiplier = 0.5 + (avg_risk * 1.5)
            base_benefit *= risk_multiplier

        # Adjust based on personalization score
        personalization_multiplier = 0.7 + (intervention.personalization_score * 0.6)

        return base_benefit * personalization_multiplier

    def _apply_intervention_benefits(self, baseline_trajectory: Dict[str, Any],
                                   intervention_benefits: Dict[str, float]) -> Dict[str, Any]:
        """Apply intervention benefits to baseline trajectory"""
        enhanced_trajectory = baseline_trajectory.copy()

        if not intervention_benefits:
            return enhanced_trajectory

        total_benefit = sum(intervention_benefits.values())

        # Apply benefits to biological age (reduce aging rate)
        for horizon in enhanced_trajectory["biological_age"]:
            years = {"6_months": 0.5, "2_years": 2, "10_years": 10}[horizon]
            age_benefit = total_benefit * 0.5 * years  # 50% of benefit applies to aging
            enhanced_trajectory["biological_age"][horizon] -= age_benefit

        # Apply benefits to disease risks (reduce risk progression)
        for horizon in enhanced_trajectory["disease_risks"]:
            for disease in enhanced_trajectory["disease_risks"][horizon]:
                risk_benefit = total_benefit * 0.3  # 30% of benefit applies to risk reduction
                current_risk = enhanced_trajectory["disease_risks"][horizon][disease]
                enhanced_trajectory["disease_risks"][horizon][disease] = max(0.0, current_risk - risk_benefit)

        return enhanced_trajectory

    def _calculate_trajectory_confidence(self, baseline: Dict[str, Any],
                                       enhanced: Dict[str, Any]) -> Dict[str, Tuple[float, float]]:
        """Calculate confidence intervals for trajectory predictions"""
        confidence_intervals = {}

        # Biological age confidence
        for horizon in baseline["biological_age"]:
            baseline_age = baseline["biological_age"][horizon]
            enhanced_age = enhanced["biological_age"][horizon]

            # Confidence decreases with time horizon
            uncertainty = {"6_months": 0.5, "2_years": 1.5, "10_years": 4.0}[horizon]

            confidence_intervals[f"biological_age_{horizon}"] = (
                enhanced_age - uncertainty,
                enhanced_age + uncertainty
            )

        return confidence_intervals

    def _calculate_health_trends(self, assessments: List[HealthAssessment]) -> Dict[str, Any]:
        """Calculate health trends from historical assessments"""
        if len(assessments) < 2:
            return {"trend_available": False, "message": "Insufficient data for trend analysis"}

        # Sort by date
        sorted_assessments = sorted(assessments, key=lambda x: x.assessment_date)

        # Calculate trends
        health_scores = [a.health_score for a in sorted_assessments]
        bio_ages = [a.biological_age for a in sorted_assessments]

        # Simple linear trend
        health_trend = (health_scores[-1] - health_scores[0]) / len(health_scores)
        bio_age_trend = (bio_ages[-1] - bio_ages[0]) / len(bio_ages)

        return {
            "trend_available": True,
            "health_score_trend": health_trend,
            "biological_age_trend": bio_age_trend,
            "trend_direction": "improving" if health_trend > 0 else "declining",
            "assessments_count": len(assessments),
            "trend_period_days": (sorted_assessments[-1].assessment_date - sorted_assessments[0].assessment_date).days
        }

    def _prioritize_risks(self, assessment: HealthAssessment) -> Dict[str, Any]:
        """Prioritize health risks based on severity and modifiability"""
        if not assessment:
            return {}

        risk_priorities = []

        for disease, risk_score in assessment.disease_risks.items():
            # Modifiability scores (how much can be improved with interventions)
            modifiability = {
                "cardiovascular_disease": 0.8,
                "diabetes_type2": 0.7,
                "alzheimer_disease": 0.4,
                "cancer": 0.5
            }.get(disease, 0.6)

            # Priority score combines risk level and modifiability
            priority_score = risk_score * 0.7 + modifiability * 0.3

            risk_priorities.append({
                "disease": disease,
                "risk_score": risk_score,
                "modifiability": modifiability,
                "priority_score": priority_score,
                "priority_level": "High" if priority_score > 0.7 else "Medium" if priority_score > 0.5 else "Low"
            })

        # Sort by priority score
        risk_priorities.sort(key=lambda x: x["priority_score"], reverse=True)

        return {
            "prioritized_risks": risk_priorities,
            "top_priority": risk_priorities[0] if risk_priorities else None,
            "high_priority_count": len([r for r in risk_priorities if r["priority_level"] == "High"])
        }

    def _assess_intervention_effectiveness(self, interventions: List[PersonalizedIntervention],
                                         trajectories: List[HealthTrajectory]) -> Dict[str, Any]:
        """Assess effectiveness of interventions based on trajectories"""
        if not interventions or not trajectories:
            return {"effectiveness_data_available": False}

        # Get latest trajectory
        latest_trajectory = trajectories[-1]

        effectiveness_scores = []
        for intervention_id, benefit in latest_trajectory.intervention_benefits.items():
            intervention = next((i for i in interventions if i.intervention_id == intervention_id), None)
            if intervention:
                effectiveness_scores.append({
                    "intervention_name": intervention.name,
                    "intervention_type": intervention.intervention_type,
                    "expected_benefit": intervention.expected_benefit,
                    "predicted_benefit": benefit,
                    "effectiveness_ratio": benefit / max(intervention.expected_benefit, 0.01)
                })

        effectiveness_scores.sort(key=lambda x: x["predicted_benefit"], reverse=True)

        return {
            "effectiveness_data_available": True,
            "intervention_effectiveness": effectiveness_scores,
            "most_effective": effectiveness_scores[0] if effectiveness_scores else None,
            "average_effectiveness": sum(e["effectiveness_ratio"] for e in effectiveness_scores) / max(len(effectiveness_scores), 1)
        }

    def _generate_personalized_recommendations(self, profile: HealthProfile,
                                             assessment: HealthAssessment,
                                             risk_priorities: Dict[str, Any]) -> List[str]:
        """Generate personalized recommendations based on profile and assessment"""
        recommendations = []

        if not assessment:
            return ["Complete health assessment to receive personalized recommendations"]

        # Age-based recommendations
        if assessment.age_acceleration > 2:
            recommendations.append("Focus on anti-aging interventions - biological age is significantly higher than chronological age")
        elif assessment.age_acceleration < -2:
            recommendations.append("Maintain current lifestyle - biological age is younger than chronological age")

        # Risk-based recommendations
        if risk_priorities.get("top_priority"):
            top_risk = risk_priorities["top_priority"]
            recommendations.append(f"Prioritize interventions for {top_risk['disease'].replace('_', ' ')} (highest risk: {top_risk['risk_score']:.2f})")

        # Health score recommendations
        if assessment.health_score < 60:
            recommendations.append("Consider comprehensive lifestyle overhaul - health score indicates significant room for improvement")
        elif assessment.health_score > 85:
            recommendations.append("Focus on optimization and maintenance - excellent health score achieved")

        # Goal-based recommendations
        if "longevity" in profile.health_goals:
            recommendations.append("Implement evidence-based longevity protocols including exercise, nutrition, and stress management")

        if "disease_prevention" in profile.health_goals:
            recommendations.append("Focus on preventive interventions based on your highest risk factors")

        return recommendations[:5]  # Limit to top 5 recommendations

    def _summarize_trajectories(self, trajectories: List[HealthTrajectory]) -> Dict[str, Any]:
        """Summarize health trajectories"""
        if not trajectories:
            return {"trajectories_available": False}

        latest_trajectory = trajectories[-1]

        # Calculate trajectory summary
        bio_age_10yr = latest_trajectory.predicted_biological_age.get("10_years", 0)
        current_bio_age = latest_trajectory.predicted_biological_age.get("6_months", 0)

        aging_rate = (bio_age_10yr - current_bio_age) / 10 if bio_age_10yr and current_bio_age else 1.0

        return {
            "trajectories_available": True,
            "latest_trajectory_date": latest_trajectory.prediction_date,
            "predicted_aging_rate": aging_rate,
            "aging_assessment": "Slower than normal" if aging_rate < 0.8 else "Normal" if aging_rate < 1.2 else "Faster than normal",
            "intervention_count": len(latest_trajectory.intervention_benefits),
            "total_intervention_benefit": sum(latest_trajectory.intervention_benefits.values())
        }

# Global health intelligence instance
health_intelligence = PersonalizedHealthIntelligence()
