"""
Literature-Informed Prediction Engine
Phase 2: Deep Scientific Integration - Literature Integration

This module implements literature-informed predictions by combining
LifeMindML model outputs with scientific literature evidence.
"""

import sys
import os
import json
import requests
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import xml.etree.ElementTree as ET
import re
from collections import defaultdict
import numpy as np

# Add project paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

@dataclass
class LiteratureEvidence:
    """Data class for literature evidence"""
    pmid: str
    title: str
    abstract: str
    authors: List[str]
    journal: str
    publication_date: str
    relevance_score: float
    evidence_type: str  # 'supporting', 'contradicting', 'neutral'
    key_findings: List[str]
    confidence: float

@dataclass
class EnhancedPrediction:
    """Data class for literature-enhanced predictions"""
    original_prediction: Any
    literature_evidence: List[LiteratureEvidence]
    combined_confidence: float
    evidence_summary: str
    recommendation_strength: str
    literature_support_score: float

class PubMedSearchEngine:
    """Interface to PubMed for literature search"""
    
    def __init__(self):
        self.base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/"
        self.rate_limit_delay = 0.34  # NCBI rate limit: 3 requests per second
        self.last_request_time = 0
    
    def _rate_limit(self):
        """Enforce rate limiting for NCBI API"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            time.sleep(self.rate_limit_delay - time_since_last)
        self.last_request_time = time.time()
    
    def search_pubmed(self, query: str, max_results: int = 20, years_back: int = 10) -> List[str]:
        """
        Search PubMed for relevant articles
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            years_back: How many years back to search
            
        Returns:
            List of PubMed IDs
        """
        self._rate_limit()
        
        # Construct search URL
        search_url = f"{self.base_url}esearch.fcgi"
        
        # Add date filter
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365 * years_back)
        date_filter = f"AND {start_date.year}:{end_date.year}[pdat]"
        
        params = {
            'db': 'pubmed',
            'term': f"{query}{date_filter}",
            'retmax': max_results,
            'retmode': 'xml',
            'sort': 'relevance'
        }
        
        try:
            response = requests.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.content)
            pmids = []
            
            for id_elem in root.findall('.//Id'):
                pmids.append(id_elem.text)
            
            return pmids
            
        except Exception as e:
            print(f"PubMed search error: {e}")
            return []
    
    def fetch_article_details(self, pmids: List[str]) -> List[Dict[str, Any]]:
        """
        Fetch detailed information for articles
        
        Args:
            pmids: List of PubMed IDs
            
        Returns:
            List of article details
        """
        if not pmids:
            return []
        
        self._rate_limit()
        
        # Construct fetch URL
        fetch_url = f"{self.base_url}efetch.fcgi"
        
        params = {
            'db': 'pubmed',
            'id': ','.join(pmids),
            'retmode': 'xml'
        }
        
        try:
            response = requests.get(fetch_url, params=params, timeout=15)
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.content)
            articles = []
            
            for article in root.findall('.//PubmedArticle'):
                article_data = self._parse_article_xml(article)
                if article_data:
                    articles.append(article_data)
            
            return articles
            
        except Exception as e:
            print(f"Article fetch error: {e}")
            return []
    
    def _parse_article_xml(self, article_elem) -> Optional[Dict[str, Any]]:
        """Parse individual article XML"""
        try:
            # Extract PMID
            pmid_elem = article_elem.find('.//PMID')
            pmid = pmid_elem.text if pmid_elem is not None else ""
            
            # Extract title
            title_elem = article_elem.find('.//ArticleTitle')
            title = title_elem.text if title_elem is not None else ""
            
            # Extract abstract
            abstract_elem = article_elem.find('.//AbstractText')
            abstract = abstract_elem.text if abstract_elem is not None else ""
            
            # Extract authors
            authors = []
            for author in article_elem.findall('.//Author'):
                lastname = author.find('LastName')
                firstname = author.find('ForeName')
                if lastname is not None and firstname is not None:
                    authors.append(f"{firstname.text} {lastname.text}")
            
            # Extract journal
            journal_elem = article_elem.find('.//Journal/Title')
            journal = journal_elem.text if journal_elem is not None else ""
            
            # Extract publication date
            pub_date = article_elem.find('.//PubDate/Year')
            pub_year = pub_date.text if pub_date is not None else ""
            
            return {
                'pmid': pmid,
                'title': title,
                'abstract': abstract,
                'authors': authors,
                'journal': journal,
                'publication_date': pub_year
            }
            
        except Exception as e:
            print(f"Article parsing error: {e}")
            return None

class LiteratureAnalyzer:
    """Analyzes literature evidence for predictions"""
    
    def __init__(self):
        self.pubmed_engine = PubMedSearchEngine()
        self.evidence_keywords = {
            'supporting': [
                'increased', 'improved', 'enhanced', 'beneficial', 'positive',
                'effective', 'significant improvement', 'extends', 'prolongs'
            ],
            'contradicting': [
                'decreased', 'reduced', 'harmful', 'negative', 'ineffective',
                'no effect', 'no improvement', 'shortens', 'detrimental'
            ],
            'neutral': [
                'no significant', 'unclear', 'mixed results', 'inconclusive',
                'requires further', 'limited evidence'
            ]
        }
    
    def analyze_drug_literature(self, compound_name: str, smiles: str, prediction_score: float) -> EnhancedPrediction:
        """
        Analyze literature evidence for drug lifespan effects
        
        Args:
            compound_name: Name of the compound
            smiles: SMILES notation
            prediction_score: Original ML prediction score
            
        Returns:
            Enhanced prediction with literature evidence
        """
        # Search for literature
        search_queries = [
            f"{compound_name} longevity aging lifespan",
            f"{compound_name} anti-aging healthspan",
            f"{compound_name} mortality survival"
        ]
        
        all_evidence = []
        
        for query in search_queries:
            pmids = self.pubmed_engine.search_pubmed(query, max_results=10)
            if pmids:
                articles = self.pubmed_engine.fetch_article_details(pmids)
                
                for article in articles:
                    evidence = self._extract_evidence_from_article(article, compound_name)
                    if evidence:
                        all_evidence.append(evidence)
        
        # Combine evidence with original prediction
        return self._combine_prediction_with_literature(
            prediction_score, all_evidence, compound_name
        )
    
    def analyze_lifestyle_literature(self, lifestyle_factors: Dict[str, Any], bio_age: float) -> EnhancedPrediction:
        """
        Analyze literature evidence for lifestyle interventions
        
        Args:
            lifestyle_factors: Dict of lifestyle parameters
            bio_age: Predicted biological age
            
        Returns:
            Enhanced prediction with literature evidence
        """
        search_queries = []
        
        # Generate queries based on lifestyle factors
        if lifestyle_factors.get('sleep_hours', 0) < 7:
            search_queries.append("sleep deprivation aging biological age")
        if lifestyle_factors.get('steps', 0) < 8000:
            search_queries.append("physical activity exercise longevity aging")
        if lifestyle_factors.get('protein', 0) < 60:
            search_queries.append("protein intake aging muscle health longevity")
        
        # Default query
        search_queries.append("lifestyle interventions healthy aging longevity")
        
        all_evidence = []
        
        for query in search_queries:
            pmids = self.pubmed_engine.search_pubmed(query, max_results=8)
            if pmids:
                articles = self.pubmed_engine.fetch_article_details(pmids)
                
                for article in articles:
                    evidence = self._extract_evidence_from_article(article, "lifestyle")
                    if evidence:
                        all_evidence.append(evidence)
        
        return self._combine_prediction_with_literature(
            bio_age, all_evidence, "lifestyle_intervention"
        )
    
    def _extract_evidence_from_article(self, article: Dict[str, Any], topic: str) -> Optional[LiteratureEvidence]:
        """Extract evidence from a single article"""
        try:
            title = article.get('title', '')
            abstract = article.get('abstract', '')
            text = f"{title} {abstract}".lower()
            
            # Calculate relevance score
            relevance_score = self._calculate_relevance_score(text, topic)
            
            if relevance_score < 0.3:  # Skip irrelevant articles
                return None
            
            # Determine evidence type
            evidence_type = self._classify_evidence_type(text)
            
            # Extract key findings
            key_findings = self._extract_key_findings(text)
            
            # Calculate confidence
            confidence = self._calculate_evidence_confidence(text, article)
            
            return LiteratureEvidence(
                pmid=article.get('pmid', ''),
                title=article.get('title', ''),
                abstract=article.get('abstract', ''),
                authors=article.get('authors', []),
                journal=article.get('journal', ''),
                publication_date=article.get('publication_date', ''),
                relevance_score=relevance_score,
                evidence_type=evidence_type,
                key_findings=key_findings,
                confidence=confidence
            )
            
        except Exception as e:
            print(f"Evidence extraction error: {e}")
            return None
    
    def _calculate_relevance_score(self, text: str, topic: str) -> float:
        """Calculate how relevant an article is to the topic"""
        relevance_keywords = {
            'longevity': ['longevity', 'lifespan', 'aging', 'healthspan', 'mortality'],
            'lifestyle': ['exercise', 'diet', 'sleep', 'nutrition', 'physical activity'],
            'compounds': ['compound', 'drug', 'molecule', 'treatment', 'intervention']
        }
        
        score = 0.0
        total_keywords = 0
        
        for category, keywords in relevance_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    score += 1
                    if topic.lower() in keyword or keyword in topic.lower():
                        score += 0.5  # Bonus for topic-specific keywords
                total_keywords += 1
        
        return min(score / max(total_keywords, 1), 1.0)
    
    def _classify_evidence_type(self, text: str) -> str:
        """Classify evidence as supporting, contradicting, or neutral"""
        scores = {'supporting': 0, 'contradicting': 0, 'neutral': 0}
        
        for evidence_type, keywords in self.evidence_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    scores[evidence_type] += 1
        
        # Return the type with highest score
        return max(scores, key=scores.get)
    
    def _extract_key_findings(self, text: str) -> List[str]:
        """Extract key findings from article text"""
        findings = []
        
        # Look for sentences with statistical significance
        stat_patterns = [
            r'[^.]*p\s*[<>=]\s*0\.\d+[^.]*',
            r'[^.]*significant[^.]*',
            r'[^.]*\d+%[^.]*improvement[^.]*',
            r'[^.]*increased.*by.*\d+[^.]*'
        ]
        
        for pattern in stat_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            findings.extend(matches[:2])  # Limit to 2 findings per pattern
        
        return findings[:5]  # Limit total findings
    
    def _calculate_evidence_confidence(self, text: str, article: Dict[str, Any]) -> float:
        """Calculate confidence in the evidence"""
        confidence = 0.5  # Base confidence
        
        # Journal quality indicators
        high_impact_journals = ['nature', 'science', 'cell', 'nejm', 'lancet']
        journal = article.get('journal', '').lower()
        
        if any(j in journal for j in high_impact_journals):
            confidence += 0.2
        
        # Study type indicators
        if any(term in text for term in ['randomized', 'controlled trial', 'meta-analysis']):
            confidence += 0.2
        
        # Sample size indicators
        if any(term in text for term in ['n=', 'participants', 'subjects']):
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _combine_prediction_with_literature(self, original_prediction: Any, 
                                          evidence_list: List[LiteratureEvidence],
                                          topic: str) -> EnhancedPrediction:
        """Combine ML prediction with literature evidence"""
        if not evidence_list:
            return EnhancedPrediction(
                original_prediction=original_prediction,
                literature_evidence=[],
                combined_confidence=0.5,
                evidence_summary="No relevant literature found",
                recommendation_strength="Low",
                literature_support_score=0.0
            )
        
        # Calculate literature support score
        support_score = 0.0
        total_weight = 0.0
        
        for evidence in evidence_list:
            weight = evidence.relevance_score * evidence.confidence
            
            if evidence.evidence_type == 'supporting':
                support_score += weight
            elif evidence.evidence_type == 'contradicting':
                support_score -= weight
            # Neutral evidence doesn't change score
            
            total_weight += weight
        
        literature_support_score = support_score / max(total_weight, 1.0)
        
        # Combine with original prediction
        if isinstance(original_prediction, (int, float)):
            # For numerical predictions (like probability scores)
            combined_confidence = (original_prediction + literature_support_score) / 2
        else:
            # For other prediction types
            combined_confidence = (0.5 + literature_support_score) / 2
        
        # Generate evidence summary
        evidence_summary = self._generate_evidence_summary(evidence_list)
        
        # Determine recommendation strength
        if combined_confidence > 0.7:
            recommendation_strength = "Strong"
        elif combined_confidence > 0.5:
            recommendation_strength = "Moderate"
        else:
            recommendation_strength = "Weak"
        
        return EnhancedPrediction(
            original_prediction=original_prediction,
            literature_evidence=evidence_list,
            combined_confidence=combined_confidence,
            evidence_summary=evidence_summary,
            recommendation_strength=recommendation_strength,
            literature_support_score=literature_support_score
        )
    
    def _generate_evidence_summary(self, evidence_list: List[LiteratureEvidence]) -> str:
        """Generate a summary of literature evidence"""
        if not evidence_list:
            return "No literature evidence available"
        
        supporting = len([e for e in evidence_list if e.evidence_type == 'supporting'])
        contradicting = len([e for e in evidence_list if e.evidence_type == 'contradicting'])
        neutral = len([e for e in evidence_list if e.evidence_type == 'neutral'])
        
        summary = f"Literature analysis of {len(evidence_list)} studies: "
        summary += f"{supporting} supporting, {contradicting} contradicting, {neutral} neutral. "
        
        if supporting > contradicting:
            summary += "Overall evidence is supportive."
        elif contradicting > supporting:
            summary += "Overall evidence is contradictory."
        else:
            summary += "Evidence is mixed."
        
        return summary

# Global literature analyzer instance
literature_analyzer = LiteratureAnalyzer()
