"""
Enhanced Prediction Engine
Phase 2: Deep Scientific Integration - Literature + Knowledge Graph Integration

This module combines LifeMindML predictions with literature evidence
and knowledge graph insights for comprehensive biomedical analysis.
"""

import sys
import os
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import numpy as np
from datetime import datetime

# Add project paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

try:
    from Biomni.biomni.integration.literature_engine import literature_analyzer, EnhancedPrediction
    from Biomni.biomni.integration.knowledge_graph import biomedical_kg, PathwayAnalysis
    from src.drug_model import predict_lifespan_effect
    from src.lifestyle_model import predict_bio_age
    from src.survival_model import predict_survival
except ImportError as e:
    print(f"Warning: Could not import required modules: {e}")

@dataclass
class ComprehensiveAnalysis:
    """Complete analysis combining ML, literature, and knowledge graph"""
    original_prediction: Any
    ml_confidence: float
    literature_evidence: Any
    pathway_analysis: List[PathwayAnalysis]
    knowledge_graph_insights: Dict[str, Any]
    combined_confidence: float
    mechanistic_explanation: str
    clinical_recommendations: List[str]
    research_priorities: List[str]
    evidence_strength: str

class EnhancedPredictionEngine:
    """Main engine for enhanced predictions"""
    
    def __init__(self):
        self.literature_analyzer = literature_analyzer
        self.knowledge_graph = biomedical_kg
        
    def enhanced_drug_prediction(self, compound_name: str, smiles: str, 
                                include_literature: bool = True,
                                include_pathways: bool = True) -> ComprehensiveAnalysis:
        """
        Enhanced drug prediction combining ML, literature, and pathways
        
        Args:
            compound_name: Name of the compound
            smiles: SMILES notation
            include_literature: Whether to include literature analysis
            include_pathways: Whether to include pathway analysis
            
        Returns:
            Comprehensive analysis results
        """
        # Step 1: Original ML prediction
        ml_prediction = predict_lifespan_effect(smiles)
        ml_confidence = self._calculate_ml_confidence(ml_prediction)
        
        # Step 2: Literature analysis
        literature_evidence = None
        if include_literature:
            try:
                literature_evidence = self.literature_analyzer.analyze_drug_literature(
                    compound_name, smiles, ml_prediction
                )
            except Exception as e:
                print(f"Literature analysis failed: {e}")
        
        # Step 3: Pathway analysis
        pathway_analysis = []
        knowledge_insights = {}
        if include_pathways:
            try:
                # Add compound to knowledge graph
                self.knowledge_graph.add_compound_to_graph(compound_name, smiles, ml_prediction)
                
                # Analyze pathways
                pathway_analysis = self.knowledge_graph.analyze_compound_pathways(compound_name)
                
                # Get intervention targets
                knowledge_insights = self.knowledge_graph.find_intervention_targets(
                    compound_name, {}
                )
            except Exception as e:
                print(f"Pathway analysis failed: {e}")
        
        # Step 4: Generate mechanistic explanation
        mechanistic_explanation = self._generate_mechanistic_explanation(
            compound_name, ml_prediction, pathway_analysis, literature_evidence
        )
        
        # Step 5: Combine evidence
        combined_confidence = self._calculate_combined_confidence(
            ml_prediction, literature_evidence, pathway_analysis
        )
        
        # Step 6: Generate recommendations
        clinical_recommendations = self._generate_clinical_recommendations(
            compound_name, combined_confidence, pathway_analysis
        )
        
        research_priorities = self._generate_research_priorities(
            compound_name, literature_evidence, pathway_analysis
        )
        
        # Step 7: Assess evidence strength
        evidence_strength = self._assess_evidence_strength(
            ml_confidence, literature_evidence, pathway_analysis
        )
        
        return ComprehensiveAnalysis(
            original_prediction=ml_prediction,
            ml_confidence=ml_confidence,
            literature_evidence=literature_evidence,
            pathway_analysis=pathway_analysis,
            knowledge_graph_insights=knowledge_insights,
            combined_confidence=combined_confidence,
            mechanistic_explanation=mechanistic_explanation,
            clinical_recommendations=clinical_recommendations,
            research_priorities=research_priorities,
            evidence_strength=evidence_strength
        )
    
    def enhanced_lifestyle_prediction(self, lifestyle_factors: Dict[str, Any],
                                    include_literature: bool = True,
                                    include_pathways: bool = True) -> ComprehensiveAnalysis:
        """
        Enhanced lifestyle prediction with literature and pathway analysis
        
        Args:
            lifestyle_factors: Dict of lifestyle parameters
            include_literature: Whether to include literature analysis
            include_pathways: Whether to include pathway analysis
            
        Returns:
            Comprehensive analysis results
        """
        # Step 1: Original ML prediction
        ml_prediction = predict_bio_age(lifestyle_factors)
        ml_confidence = self._calculate_ml_confidence(ml_prediction, prediction_type="bio_age")
        
        # Step 2: Literature analysis
        literature_evidence = None
        if include_literature:
            try:
                literature_evidence = self.literature_analyzer.analyze_lifestyle_literature(
                    lifestyle_factors, ml_prediction
                )
            except Exception as e:
                print(f"Literature analysis failed: {e}")
        
        # Step 3: Pathway analysis
        pathway_analysis = []
        knowledge_insights = {}
        if include_pathways:
            try:
                # Analyze disease risk factors
                disease_risks = self.knowledge_graph.analyze_disease_risk_factors(lifestyle_factors)
                knowledge_insights["disease_risks"] = disease_risks
                
                # Analyze lifestyle interventions
                lifestyle_targets = self._analyze_lifestyle_pathways(lifestyle_factors)
                knowledge_insights["lifestyle_targets"] = lifestyle_targets
                
            except Exception as e:
                print(f"Pathway analysis failed: {e}")
        
        # Step 4: Generate mechanistic explanation
        mechanistic_explanation = self._generate_lifestyle_mechanistic_explanation(
            lifestyle_factors, ml_prediction, knowledge_insights, literature_evidence
        )
        
        # Step 5: Combine evidence
        combined_confidence = self._calculate_combined_confidence(
            ml_prediction, literature_evidence, pathway_analysis
        )
        
        # Step 6: Generate recommendations
        clinical_recommendations = self._generate_lifestyle_recommendations(
            lifestyle_factors, knowledge_insights
        )
        
        research_priorities = self._generate_lifestyle_research_priorities(
            lifestyle_factors, literature_evidence
        )
        
        # Step 7: Assess evidence strength
        evidence_strength = self._assess_evidence_strength(
            ml_confidence, literature_evidence, pathway_analysis
        )
        
        return ComprehensiveAnalysis(
            original_prediction=ml_prediction,
            ml_confidence=ml_confidence,
            literature_evidence=literature_evidence,
            pathway_analysis=pathway_analysis,
            knowledge_graph_insights=knowledge_insights,
            combined_confidence=combined_confidence,
            mechanistic_explanation=mechanistic_explanation,
            clinical_recommendations=clinical_recommendations,
            research_priorities=research_priorities,
            evidence_strength=evidence_strength
        )
    
    def multi_modal_health_analysis(self, patient_data: Dict[str, Any]) -> Dict[str, ComprehensiveAnalysis]:
        """
        Comprehensive multi-modal health analysis
        
        Args:
            patient_data: Dict containing lifestyle, clinical, and other data
            
        Returns:
            Dict of analysis results for each modality
        """
        results = {}
        
        # Lifestyle analysis
        if 'lifestyle' in patient_data:
            results['lifestyle'] = self.enhanced_lifestyle_prediction(
                patient_data['lifestyle']
            )
        
        # Compound analysis (if supplements/medications provided)
        if 'compounds' in patient_data:
            results['compounds'] = {}
            for compound_name, smiles in patient_data['compounds'].items():
                results['compounds'][compound_name] = self.enhanced_drug_prediction(
                    compound_name, smiles
                )
        
        # Clinical risk analysis
        if 'clinical' in patient_data:
            results['clinical'] = self._analyze_clinical_data(patient_data['clinical'])
        
        # Generate integrated insights
        results['integrated_insights'] = self._generate_integrated_insights(results)
        
        return results
    
    def _calculate_ml_confidence(self, prediction: Any, prediction_type: str = "probability") -> float:
        """Calculate confidence in ML prediction"""
        if prediction_type == "probability":
            # For probability scores, confidence is higher at extremes
            if isinstance(prediction, (int, float)):
                return 1.0 - 2 * abs(prediction - 0.5)
        elif prediction_type == "bio_age":
            # For biological age, assume moderate confidence
            return 0.7
        
        return 0.5  # Default confidence
    
    def _generate_mechanistic_explanation(self, compound_name: str, ml_prediction: float,
                                        pathway_analysis: List[PathwayAnalysis],
                                        literature_evidence: Any) -> str:
        """Generate comprehensive mechanistic explanation"""
        explanation = f"## 🧬 Mechanistic Analysis: {compound_name}\n\n"
        
        # ML prediction context
        explanation += f"**Machine Learning Prediction:** {ml_prediction:.3f}\n"
        if ml_prediction > 0.7:
            explanation += "- High probability of lifespan extension effects\n"
        elif ml_prediction > 0.5:
            explanation += "- Moderate probability of beneficial effects\n"
        else:
            explanation += "- Limited evidence for lifespan extension\n"
        
        explanation += "\n"
        
        # Pathway analysis
        if pathway_analysis:
            explanation += "**Molecular Pathway Analysis:**\n"
            for pathway in pathway_analysis[:3]:  # Top 3 pathways
                explanation += f"- **{pathway.pathway_name}** (Score: {pathway.pathway_score:.2f})\n"
                explanation += f"  - Mechanism: {pathway.mechanism_description}\n"
                explanation += f"  - Key targets: {', '.join(pathway.therapeutic_targets[:3])}\n"
            explanation += "\n"
        
        # Literature evidence
        if literature_evidence and hasattr(literature_evidence, 'evidence_summary'):
            explanation += f"**Literature Evidence:**\n"
            explanation += f"- {literature_evidence.evidence_summary}\n"
            explanation += f"- Literature support score: {literature_evidence.literature_support_score:.2f}\n\n"
        
        # Overall assessment
        explanation += "**Integrated Assessment:**\n"
        if pathway_analysis and literature_evidence:
            avg_pathway_score = np.mean([p.pathway_score for p in pathway_analysis])
            combined_score = (ml_prediction + avg_pathway_score + literature_evidence.literature_support_score) / 3
            explanation += f"- Combined evidence score: {combined_score:.3f}\n"
            
            if combined_score > 0.75:
                explanation += "- **Strong convergent evidence** across multiple lines of investigation\n"
            elif combined_score > 0.6:
                explanation += "- **Moderate evidence** with some supporting mechanisms\n"
            else:
                explanation += "- **Limited evidence** - requires further investigation\n"
        
        return explanation
    
    def _generate_lifestyle_mechanistic_explanation(self, lifestyle_factors: Dict[str, Any],
                                                  bio_age: float, knowledge_insights: Dict[str, Any],
                                                  literature_evidence: Any) -> str:
        """Generate mechanistic explanation for lifestyle factors"""
        explanation = f"## 🧘 Lifestyle Impact Analysis\n\n"
        
        explanation += f"**Predicted Biological Age:** {bio_age:.1f} years\n\n"
        
        # Analyze individual factors
        explanation += "**Factor Analysis:**\n"
        
        sleep_hours = lifestyle_factors.get('sleep_hours', 0)
        if sleep_hours < 7:
            explanation += f"- **Sleep ({sleep_hours}h):** Below optimal range, may accelerate aging through stress pathways\n"
        elif sleep_hours > 9:
            explanation += f"- **Sleep ({sleep_hours}h):** Above optimal range, may indicate underlying health issues\n"
        else:
            explanation += f"- **Sleep ({sleep_hours}h):** Within optimal range for cellular repair and recovery\n"
        
        steps = lifestyle_factors.get('steps', 0)
        if steps < 8000:
            explanation += f"- **Activity ({steps:,} steps):** Below recommended level, missing cardiovascular and metabolic benefits\n"
        else:
            explanation += f"- **Activity ({steps:,} steps):** Good activity level supporting mitochondrial health\n"
        
        # Disease risk analysis
        if 'disease_risks' in knowledge_insights:
            explanation += "\n**Disease Risk Assessment:**\n"
            for disease, risk in knowledge_insights['disease_risks'].items():
                risk_level = "High" if risk > 0.7 else "Moderate" if risk > 0.5 else "Low"
                explanation += f"- {disease.replace('_', ' ').title()}: {risk_level} risk ({risk:.2f})\n"
        
        return explanation
    
    def _calculate_combined_confidence(self, ml_prediction: Any, literature_evidence: Any,
                                     pathway_analysis: List[PathwayAnalysis]) -> float:
        """Calculate combined confidence from all evidence sources"""
        confidences = []
        
        # ML confidence
        ml_conf = self._calculate_ml_confidence(ml_prediction)
        confidences.append(ml_conf)
        
        # Literature confidence
        if literature_evidence and hasattr(literature_evidence, 'combined_confidence'):
            confidences.append(literature_evidence.combined_confidence)
        
        # Pathway confidence
        if pathway_analysis:
            pathway_conf = np.mean([p.pathway_score for p in pathway_analysis])
            confidences.append(pathway_conf)
        
        # Weighted average (more sources = higher confidence)
        if len(confidences) > 1:
            # Give more weight when multiple sources agree
            base_confidence = np.mean(confidences)
            consensus_bonus = 0.1 * (len(confidences) - 1)  # Bonus for multiple sources
            return min(1.0, base_confidence + consensus_bonus)
        
        return confidences[0] if confidences else 0.5
    
    def _generate_clinical_recommendations(self, compound_name: str, confidence: float,
                                         pathway_analysis: List[PathwayAnalysis]) -> List[str]:
        """Generate clinical recommendations"""
        recommendations = []
        
        if confidence > 0.8:
            recommendations.append(f"Strong evidence supports clinical investigation of {compound_name}")
            recommendations.append("Consider dose-response studies in appropriate model systems")
        elif confidence > 0.6:
            recommendations.append(f"Moderate evidence warrants further research on {compound_name}")
            recommendations.append("Conduct mechanistic studies to validate pathway interactions")
        else:
            recommendations.append(f"Limited evidence for {compound_name} - proceed with caution")
            recommendations.append("Focus on basic research to establish biological plausibility")
        
        # Pathway-specific recommendations
        if pathway_analysis:
            top_pathway = pathway_analysis[0]
            recommendations.append(f"Target {top_pathway.pathway_name} for mechanistic validation")
            recommendations.append(f"Monitor biomarkers related to {', '.join(top_pathway.therapeutic_targets[:2])}")
        
        return recommendations
    
    def _generate_research_priorities(self, compound_name: str, literature_evidence: Any,
                                    pathway_analysis: List[PathwayAnalysis]) -> List[str]:
        """Generate research priorities"""
        priorities = []
        
        # Literature gaps
        if not literature_evidence or len(literature_evidence.literature_evidence) < 5:
            priorities.append("Conduct systematic literature review for comprehensive evidence")
        
        # Mechanistic studies
        if pathway_analysis:
            priorities.append("Validate pathway interactions through targeted experiments")
            priorities.append("Investigate dose-response relationships")
        
        # Clinical translation
        priorities.append("Assess safety profile and potential drug interactions")
        priorities.append("Design biomarker-driven clinical studies")
        
        return priorities
    
    def _analyze_lifestyle_pathways(self, lifestyle_factors: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze lifestyle factor pathway interactions"""
        targets = {
            "molecular_pathways": [],
            "intervention_potential": 0.0
        }
        
        # Analyze each lifestyle factor
        for factor, value in lifestyle_factors.items():
            if factor == "sleep_hours" and value < 7:
                targets["molecular_pathways"].append("circadian_rhythm_disruption")
            elif factor == "steps" and value > 8000:
                targets["molecular_pathways"].append("mitochondrial_biogenesis")
            elif factor == "protein" and value > 80:
                targets["molecular_pathways"].append("muscle_protein_synthesis")
        
        targets["intervention_potential"] = len(targets["molecular_pathways"]) / 5.0
        
        return targets
    
    def _generate_lifestyle_recommendations(self, lifestyle_factors: Dict[str, Any],
                                          knowledge_insights: Dict[str, Any]) -> List[str]:
        """Generate lifestyle-specific recommendations"""
        recommendations = []
        
        # Sleep optimization
        sleep_hours = lifestyle_factors.get('sleep_hours', 0)
        if sleep_hours < 7:
            recommendations.append("Prioritize sleep optimization: aim for 7-9 hours nightly")
            recommendations.append("Implement sleep hygiene practices and consistent schedule")
        
        # Activity recommendations
        steps = lifestyle_factors.get('steps', 0)
        if steps < 8000:
            recommendations.append("Increase daily physical activity to 8,000+ steps")
            recommendations.append("Include both aerobic and resistance training")
        
        # Disease-specific recommendations
        if 'disease_risks' in knowledge_insights:
            high_risk_diseases = [d for d, r in knowledge_insights['disease_risks'].items() if r > 0.7]
            for disease in high_risk_diseases:
                recommendations.append(f"Implement targeted interventions for {disease.replace('_', ' ')} risk reduction")
        
        return recommendations
    
    def _generate_lifestyle_research_priorities(self, lifestyle_factors: Dict[str, Any],
                                              literature_evidence: Any) -> List[str]:
        """Generate lifestyle research priorities"""
        priorities = [
            "Investigate personalized lifestyle intervention protocols",
            "Study biomarker changes in response to lifestyle modifications",
            "Develop predictive models for intervention response",
            "Examine gene-lifestyle interactions for precision recommendations"
        ]
        
        return priorities
    
    def _assess_evidence_strength(self, ml_confidence: float, literature_evidence: Any,
                                pathway_analysis: List[PathwayAnalysis]) -> str:
        """Assess overall evidence strength"""
        evidence_sources = 1  # ML always present
        
        if literature_evidence and hasattr(literature_evidence, 'literature_evidence'):
            if len(literature_evidence.literature_evidence) > 0:
                evidence_sources += 1
        
        if pathway_analysis and len(pathway_analysis) > 0:
            evidence_sources += 1
        
        avg_confidence = ml_confidence
        if literature_evidence and hasattr(literature_evidence, 'combined_confidence'):
            avg_confidence = (avg_confidence + literature_evidence.combined_confidence) / 2
        
        if evidence_sources >= 3 and avg_confidence > 0.8:
            return "Very Strong"
        elif evidence_sources >= 2 and avg_confidence > 0.7:
            return "Strong"
        elif avg_confidence > 0.6:
            return "Moderate"
        else:
            return "Weak"
    
    def _analyze_clinical_data(self, clinical_data: Dict[str, Any]) -> ComprehensiveAnalysis:
        """Analyze clinical data with enhanced insights"""
        # This would integrate with survival analysis and clinical pathways
        # For now, return a simplified analysis
        return ComprehensiveAnalysis(
            original_prediction=clinical_data,
            ml_confidence=0.7,
            literature_evidence=None,
            pathway_analysis=[],
            knowledge_graph_insights={},
            combined_confidence=0.7,
            mechanistic_explanation="Clinical data analysis",
            clinical_recommendations=["Regular monitoring recommended"],
            research_priorities=["Validate clinical biomarkers"],
            evidence_strength="Moderate"
        )
    
    def _generate_integrated_insights(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate integrated insights across all modalities"""
        insights = {
            "overall_health_score": 0.0,
            "key_recommendations": [],
            "research_opportunities": [],
            "intervention_priorities": []
        }
        
        # Calculate overall health score
        confidences = []
        for modality, analysis in results.items():
            if modality != 'integrated_insights' and hasattr(analysis, 'combined_confidence'):
                confidences.append(analysis.combined_confidence)
        
        if confidences:
            insights["overall_health_score"] = np.mean(confidences)
        
        # Aggregate recommendations
        for modality, analysis in results.items():
            if modality != 'integrated_insights' and hasattr(analysis, 'clinical_recommendations'):
                insights["key_recommendations"].extend(analysis.clinical_recommendations[:2])
        
        return insights

# Global enhanced prediction engine
enhanced_engine = EnhancedPredictionEngine()
