"""
Biomedical Knowledge Graph Integration
Phase 2: Deep Scientific Integration - Knowledge Graph System

This module implements knowledge graph integration for connecting
molecular pathways, disease networks, and lifestyle interactions.
"""

import sys
import os
import json
import requests
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import networkx as nx
import numpy as np
from datetime import datetime

# Add project paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

@dataclass
class KnowledgeNode:
    """Represents a node in the knowledge graph"""
    id: str
    type: str  # 'compound', 'pathway', 'disease', 'gene', 'protein', 'lifestyle_factor'
    name: str
    description: str
    properties: Dict[str, Any]
    confidence: float

@dataclass
class KnowledgeEdge:
    """Represents an edge in the knowledge graph"""
    source_id: str
    target_id: str
    relationship_type: str  # 'targets', 'regulates', 'associated_with', 'causes', 'prevents'
    evidence_score: float
    evidence_sources: List[str]
    properties: Dict[str, Any]

@dataclass
class PathwayAnalysis:
    """Results of pathway analysis"""
    pathway_id: str
    pathway_name: str
    affected_genes: List[str]
    pathway_score: float
    mechanism_description: str
    therapeutic_targets: List[str]

class BiomedicalKnowledgeGraph:
    """Main knowledge graph for biomedical data integration"""
    
    def __init__(self):
        self.graph = nx.MultiDiGraph()
        self.node_index = {}  # Fast lookup by node ID
        self.pathway_database = self._initialize_pathway_database()
        self.disease_network = self._initialize_disease_network()
        self.lifestyle_interactions = self._initialize_lifestyle_interactions()
        
    def _initialize_pathway_database(self) -> Dict[str, Dict[str, Any]]:
        """Initialize molecular pathway database"""
        return {
            "aging_pathways": {
                "mtor_pathway": {
                    "name": "mTOR Signaling Pathway",
                    "description": "Mechanistic target of rapamycin pathway regulating cell growth and aging",
                    "genes": ["MTOR", "TSC1", "TSC2", "AKT1", "RICTOR", "RAPTOR"],
                    "compounds": ["rapamycin", "metformin", "resveratrol"],
                    "aging_relevance": 0.95,
                    "therapeutic_potential": 0.90
                },
                "sirtuin_pathway": {
                    "name": "Sirtuin Signaling Pathway", 
                    "description": "NAD+-dependent deacetylases involved in longevity regulation",
                    "genes": ["SIRT1", "SIRT3", "SIRT6", "NAMPT", "PARP1"],
                    "compounds": ["resveratrol", "nicotinamide", "nad+"],
                    "aging_relevance": 0.92,
                    "therapeutic_potential": 0.85
                },
                "autophagy_pathway": {
                    "name": "Autophagy Pathway",
                    "description": "Cellular degradation pathway crucial for healthy aging",
                    "genes": ["ATG5", "ATG7", "BECN1", "LC3B", "ULK1"],
                    "compounds": ["spermidine", "rapamycin", "metformin"],
                    "aging_relevance": 0.88,
                    "therapeutic_potential": 0.82
                },
                "insulin_signaling": {
                    "name": "Insulin/IGF-1 Signaling Pathway",
                    "description": "Growth factor signaling pathway linked to longevity",
                    "genes": ["IGF1", "IGF1R", "IRS1", "FOXO1", "FOXO3"],
                    "compounds": ["metformin", "acarbose"],
                    "aging_relevance": 0.90,
                    "therapeutic_potential": 0.88
                }
            },
            "metabolic_pathways": {
                "glycolysis": {
                    "name": "Glycolysis Pathway",
                    "description": "Glucose metabolism pathway",
                    "genes": ["HK1", "PFKM", "ALDOA", "PKM", "LDHA"],
                    "compounds": ["metformin", "2-deoxyglucose"],
                    "aging_relevance": 0.75,
                    "therapeutic_potential": 0.70
                },
                "oxidative_phosphorylation": {
                    "name": "Oxidative Phosphorylation",
                    "description": "Mitochondrial energy production pathway",
                    "genes": ["COX1", "COX2", "ATP5A1", "NDUFA1", "SDHA"],
                    "compounds": ["coq10", "nicotinamide", "pqq"],
                    "aging_relevance": 0.85,
                    "therapeutic_potential": 0.78
                }
            }
        }
    
    def _initialize_disease_network(self) -> Dict[str, Dict[str, Any]]:
        """Initialize disease-biomarker network"""
        return {
            "cardiovascular_disease": {
                "biomarkers": ["LDL", "HDL", "CRP", "troponin", "BNP"],
                "risk_factors": ["hypertension", "diabetes", "smoking", "obesity"],
                "pathways": ["inflammation", "lipid_metabolism", "endothelial_function"],
                "lifestyle_factors": ["exercise", "diet", "stress_management"],
                "severity_score": 0.85
            },
            "diabetes_type2": {
                "biomarkers": ["glucose", "HbA1c", "insulin", "c_peptide"],
                "risk_factors": ["obesity", "sedentary_lifestyle", "family_history"],
                "pathways": ["insulin_signaling", "glucose_metabolism"],
                "lifestyle_factors": ["diet", "exercise", "weight_management"],
                "severity_score": 0.80
            },
            "alzheimer_disease": {
                "biomarkers": ["amyloid_beta", "tau", "APOE4", "neurofilament"],
                "risk_factors": ["age", "genetics", "cardiovascular_disease"],
                "pathways": ["amyloid_cascade", "tau_pathology", "neuroinflammation"],
                "lifestyle_factors": ["cognitive_stimulation", "exercise", "social_engagement"],
                "severity_score": 0.90
            },
            "cancer": {
                "biomarkers": ["PSA", "CEA", "CA125", "AFP", "tumor_markers"],
                "risk_factors": ["smoking", "radiation", "genetics", "age"],
                "pathways": ["DNA_repair", "cell_cycle", "apoptosis", "angiogenesis"],
                "lifestyle_factors": ["diet", "exercise", "smoking_cessation"],
                "severity_score": 0.95
            }
        }
    
    def _initialize_lifestyle_interactions(self) -> Dict[str, Dict[str, Any]]:
        """Initialize lifestyle factor interactions"""
        return {
            "exercise": {
                "molecular_targets": ["AMPK", "PGC1A", "SIRT1", "mTOR"],
                "pathways_affected": ["mitochondrial_biogenesis", "autophagy", "insulin_sensitivity"],
                "biomarkers_improved": ["VO2_max", "insulin_sensitivity", "inflammation_markers"],
                "aging_benefit_score": 0.88,
                "evidence_strength": 0.95
            },
            "caloric_restriction": {
                "molecular_targets": ["SIRT1", "AMPK", "FOXO", "mTOR"],
                "pathways_affected": ["autophagy", "stress_resistance", "metabolic_efficiency"],
                "biomarkers_improved": ["glucose", "insulin", "IGF1", "inflammation"],
                "aging_benefit_score": 0.92,
                "evidence_strength": 0.90
            },
            "sleep_optimization": {
                "molecular_targets": ["melatonin", "growth_hormone", "cortisol"],
                "pathways_affected": ["circadian_rhythm", "DNA_repair", "immune_function"],
                "biomarkers_improved": ["cortisol", "growth_hormone", "immune_markers"],
                "aging_benefit_score": 0.82,
                "evidence_strength": 0.85
            },
            "stress_management": {
                "molecular_targets": ["cortisol", "BDNF", "serotonin", "GABA"],
                "pathways_affected": ["HPA_axis", "neuroplasticity", "inflammation"],
                "biomarkers_improved": ["cortisol", "inflammation", "telomere_length"],
                "aging_benefit_score": 0.78,
                "evidence_strength": 0.80
            }
        }
    
    def add_compound_to_graph(self, compound_name: str, smiles: str, prediction_score: float) -> str:
        """Add a compound node to the knowledge graph"""
        node_id = f"compound_{compound_name.lower().replace(' ', '_')}"
        
        node = KnowledgeNode(
            id=node_id,
            type="compound",
            name=compound_name,
            description=f"Chemical compound with SMILES: {smiles}",
            properties={
                "smiles": smiles,
                "lifespan_prediction_score": prediction_score,
                "molecular_weight": self._calculate_molecular_weight(smiles),
                "drug_like": self._assess_drug_likeness(smiles)
            },
            confidence=prediction_score
        )
        
        self.graph.add_node(node_id, **asdict(node))
        self.node_index[node_id] = node
        
        # Connect to relevant pathways
        self._connect_compound_to_pathways(node_id, compound_name)
        
        return node_id
    
    def analyze_compound_pathways(self, compound_name: str) -> List[PathwayAnalysis]:
        """Analyze which pathways a compound affects"""
        analyses = []
        
        for category, pathways in self.pathway_database.items():
            for pathway_id, pathway_data in pathways.items():
                if compound_name.lower() in [c.lower() for c in pathway_data.get("compounds", [])]:
                    analysis = PathwayAnalysis(
                        pathway_id=pathway_id,
                        pathway_name=pathway_data["name"],
                        affected_genes=pathway_data["genes"],
                        pathway_score=pathway_data["aging_relevance"],
                        mechanism_description=pathway_data["description"],
                        therapeutic_targets=pathway_data["genes"][:3]  # Top 3 targets
                    )
                    analyses.append(analysis)
        
        return sorted(analyses, key=lambda x: x.pathway_score, reverse=True)
    
    def analyze_disease_risk_factors(self, lifestyle_factors: Dict[str, Any]) -> Dict[str, float]:
        """Analyze disease risk based on lifestyle factors"""
        risk_scores = {}
        
        for disease, disease_data in self.disease_network.items():
            base_risk = disease_data["severity_score"]
            risk_modifiers = 0.0
            
            # Analyze lifestyle protective factors
            for lifestyle_factor in disease_data.get("lifestyle_factors", []):
                if lifestyle_factor == "exercise" and lifestyle_factors.get("steps", 0) > 8000:
                    risk_modifiers -= 0.15
                elif lifestyle_factor == "diet" and lifestyle_factors.get("calories", 0) < 2500:
                    risk_modifiers -= 0.10
                elif lifestyle_factor == "stress_management":
                    # Assume good stress management if sleep is adequate
                    if lifestyle_factors.get("sleep_hours", 0) >= 7:
                        risk_modifiers -= 0.08
            
            # Calculate final risk score
            final_risk = max(0.0, min(1.0, base_risk + risk_modifiers))
            risk_scores[disease] = final_risk
        
        return risk_scores
    
    def find_intervention_targets(self, compound_name: str, lifestyle_factors: Dict[str, Any]) -> Dict[str, Any]:
        """Find molecular targets for intervention"""
        targets = {
            "molecular_targets": set(),
            "pathway_targets": set(),
            "lifestyle_synergies": [],
            "intervention_score": 0.0
        }
        
        # Analyze compound targets
        pathway_analyses = self.analyze_compound_pathways(compound_name)
        for analysis in pathway_analyses:
            targets["molecular_targets"].update(analysis.affected_genes)
            targets["pathway_targets"].add(analysis.pathway_name)
        
        # Analyze lifestyle synergies
        for lifestyle_factor, factor_data in self.lifestyle_interactions.items():
            # Check if lifestyle factor aligns with compound pathways
            common_targets = set(factor_data["molecular_targets"]) & targets["molecular_targets"]
            if common_targets:
                synergy_score = len(common_targets) * factor_data["aging_benefit_score"]
                targets["lifestyle_synergies"].append({
                    "factor": lifestyle_factor,
                    "common_targets": list(common_targets),
                    "synergy_score": synergy_score
                })
        
        # Calculate overall intervention score
        pathway_score = sum(a.pathway_score for a in pathway_analyses) / max(len(pathway_analyses), 1)
        lifestyle_score = sum(s["synergy_score"] for s in targets["lifestyle_synergies"]) / max(len(targets["lifestyle_synergies"]), 1)
        targets["intervention_score"] = (pathway_score + lifestyle_score) / 2
        
        # Convert sets to lists for JSON serialization
        targets["molecular_targets"] = list(targets["molecular_targets"])
        targets["pathway_targets"] = list(targets["pathway_targets"])
        
        return targets
    
    def generate_mechanistic_explanation(self, compound_name: str, prediction_score: float) -> str:
        """Generate mechanistic explanation for compound effects"""
        pathway_analyses = self.analyze_compound_pathways(compound_name)
        
        if not pathway_analyses:
            return f"Limited mechanistic information available for {compound_name}. Prediction based on structural similarity to known longevity compounds."
        
        explanation = f"**Mechanistic Analysis for {compound_name}:**\n\n"
        
        for analysis in pathway_analyses[:3]:  # Top 3 pathways
            explanation += f"**{analysis.pathway_name}** (Score: {analysis.pathway_score:.2f})\n"
            explanation += f"- {analysis.mechanism_description}\n"
            explanation += f"- Key targets: {', '.join(analysis.therapeutic_targets)}\n"
            explanation += f"- Aging relevance: {analysis.pathway_score:.1%}\n\n"
        
        # Overall assessment
        avg_pathway_score = np.mean([a.pathway_score for a in pathway_analyses])
        combined_score = (prediction_score + avg_pathway_score) / 2
        
        explanation += f"**Overall Assessment:**\n"
        explanation += f"- ML Prediction Score: {prediction_score:.3f}\n"
        explanation += f"- Pathway Evidence Score: {avg_pathway_score:.3f}\n"
        explanation += f"- Combined Confidence: {combined_score:.3f}\n"
        
        if combined_score > 0.8:
            explanation += f"- **Strong evidence** for longevity benefits through multiple pathways\n"
        elif combined_score > 0.6:
            explanation += f"- **Moderate evidence** for longevity benefits\n"
        else:
            explanation += f"- **Limited evidence** - requires further investigation\n"
        
        return explanation
    
    def _connect_compound_to_pathways(self, compound_node_id: str, compound_name: str):
        """Connect compound to relevant pathways in the graph"""
        for category, pathways in self.pathway_database.items():
            for pathway_id, pathway_data in pathways.items():
                if compound_name.lower() in [c.lower() for c in pathway_data.get("compounds", [])]:
                    # Create pathway node if it doesn't exist
                    pathway_node_id = f"pathway_{pathway_id}"
                    if pathway_node_id not in self.node_index:
                        pathway_node = KnowledgeNode(
                            id=pathway_node_id,
                            type="pathway",
                            name=pathway_data["name"],
                            description=pathway_data["description"],
                            properties=pathway_data,
                            confidence=pathway_data["aging_relevance"]
                        )
                        self.graph.add_node(pathway_node_id, **asdict(pathway_node))
                        self.node_index[pathway_node_id] = pathway_node
                    
                    # Create edge
                    edge = KnowledgeEdge(
                        source_id=compound_node_id,
                        target_id=pathway_node_id,
                        relationship_type="modulates",
                        evidence_score=pathway_data["aging_relevance"],
                        evidence_sources=["pathway_database"],
                        properties={"interaction_type": "pathway_modulation"}
                    )
                    
                    self.graph.add_edge(
                        compound_node_id, 
                        pathway_node_id, 
                        **asdict(edge)
                    )
    
    def _calculate_molecular_weight(self, smiles: str) -> float:
        """Calculate molecular weight from SMILES (simplified)"""
        # This is a simplified calculation - in practice, use RDKit
        try:
            from rdkit import Chem
            from rdkit.Chem import Descriptors
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                return Descriptors.MolWt(mol)
        except:
            pass
        return 0.0
    
    def _assess_drug_likeness(self, smiles: str) -> bool:
        """Assess if compound is drug-like (simplified)"""
        try:
            from rdkit import Chem
            from rdkit.Chem import Descriptors
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                mw = Descriptors.MolWt(mol)
                logp = Descriptors.MolLogP(mol)
                hbd = Descriptors.NumHDonors(mol)
                hba = Descriptors.NumHAcceptors(mol)
                
                # Lipinski's Rule of Five
                return (mw <= 500 and logp <= 5 and hbd <= 5 and hba <= 10)
        except:
            pass
        return False
    
    def get_pathway_network_analysis(self, compounds: List[str]) -> Dict[str, Any]:
        """Analyze pathway networks for multiple compounds"""
        all_pathways = set()
        pathway_scores = defaultdict(list)
        
        for compound in compounds:
            analyses = self.analyze_compound_pathways(compound)
            for analysis in analyses:
                all_pathways.add(analysis.pathway_id)
                pathway_scores[analysis.pathway_id].append(analysis.pathway_score)
        
        # Calculate network metrics
        network_analysis = {
            "total_pathways": len(all_pathways),
            "pathway_overlap": {},
            "synergy_score": 0.0,
            "top_pathways": []
        }
        
        # Calculate average scores for each pathway
        for pathway_id, scores in pathway_scores.items():
            avg_score = np.mean(scores)
            pathway_name = self._get_pathway_name(pathway_id)
            network_analysis["top_pathways"].append({
                "pathway_id": pathway_id,
                "pathway_name": pathway_name,
                "average_score": avg_score,
                "compound_count": len(scores)
            })
        
        # Sort by average score
        network_analysis["top_pathways"].sort(key=lambda x: x["average_score"], reverse=True)
        
        # Calculate synergy score (pathways affected by multiple compounds)
        multi_compound_pathways = [p for p in network_analysis["top_pathways"] if p["compound_count"] > 1]
        if multi_compound_pathways:
            network_analysis["synergy_score"] = np.mean([p["average_score"] for p in multi_compound_pathways])
        
        return network_analysis
    
    def _get_pathway_name(self, pathway_id: str) -> str:
        """Get pathway name from ID"""
        for category, pathways in self.pathway_database.items():
            if pathway_id in pathways:
                return pathways[pathway_id]["name"]
        return pathway_id

# Global knowledge graph instance
biomedical_kg = BiomedicalKnowledgeGraph()
