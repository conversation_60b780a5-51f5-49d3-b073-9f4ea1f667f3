"""
LifeMindML Intelligent Agent Workflows
Phase 1: Enhanced Tool Integration - Intelligent Agent Workflows

This module implements specialized biomedical agents that leverage LifeMindML tools
for compound discovery, health assessment, and longevity research.
"""

import sys
import os
from typing import Dict, List, Any, Optional, Tuple
import json
import pandas as pd
from datetime import datetime

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

try:
    from biomni.tool.lifemind_drug import LIFEMIND_TOOLS
    from biomni.tool.tool_registry import ToolRegistry
except ImportError as e:
    print(f"Warning: Could not import Biomni modules: {e}")

class CompoundDiscoveryAgent:
    """
    Intelligent agent for compound discovery and analysis
    Specializes in finding molecules with specific properties for longevity research
    """
    
    def __init__(self, tool_registry: ToolRegistry):
        self.tool_registry = tool_registry
        self.drug_tool = tool_registry.get_tool_by_name("lifespan_drug_predictor")
        self.compound_database = self._load_compound_database()
    
    def _load_compound_database(self) -> Dict[str, str]:
        """Load a database of known compounds with their SMILES"""
        return {
            "Metformin": "CN(C)C(=N)N=C(N)N",
            "Aspirin": "CC(=O)OC1=CC=CC=C1C(=O)O",
            "Resveratrol": "C1=CC(=CC=C1C=CC2=CC(=CC(=C2)O)O)O",
            "Rapamycin": "CC1CCC2CC(C(=CC=CC=CC(CC(C(=O)C(C(C(=CC(C(=O)CC(OC(=O)C3CCCCN3C(=O)C(=O)C1(O2)O)C(C)CC4CCC(C(C4)OC)O)C)C)O)OC)C)C)C)OC",
            "Curcumin": "COC1=C(C=CC(=C1)C=CC(=O)CC(=O)C=CC2=CC(=C(C=C2)O)OC)O",
            "Quercetin": "C1=CC(=C(C=C1C2=C(C(=O)C3=C(C=C(C=C3O2)O)O)O)O)O",
            "EGCG": "C1=C(C=C(C(=C1O)O)O)C2C(C(=O)C3=C(C=C(C=C3O2)O)O)OC4=CC(=C(C(=C4)O)O)O",
            "NAD+": "C1=CC(=C[N+](=C1)C2C(C(C(O2)COP(=O)([O-])OP(=O)([O-])OCC3C(C(C(O3)N4C=NC5=C(N=CN=C54)N)O)O)O)O)C(=O)N",
            "Nicotinamide": "C1=CC=NC(=C1)C(=O)N",
            "Spermidine": "C(CCN)NCCCN"
        }
    
    def find_similar_compounds(self, reference_compound: str, similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        Find compounds similar to a reference compound with high lifespan extension probability
        
        Args:
            reference_compound: SMILES string of reference compound
            similarity_threshold: Minimum probability threshold for lifespan extension
            
        Returns:
            List of similar compounds with their analysis
        """
        results = []
        
        # Analyze reference compound
        if self.drug_tool and "function" in self.drug_tool:
            reference_analysis = self.drug_tool["function"](reference_compound)
            
            results.append({
                "compound_name": "Reference Compound",
                "smiles": reference_compound,
                "analysis": reference_analysis,
                "type": "reference"
            })
        
        # Analyze database compounds
        for name, smiles in self.compound_database.items():
            if self.drug_tool and "function" in self.drug_tool:
                analysis = self.drug_tool["function"](smiles, include_properties=True)
                
                # Extract probability from analysis (simplified parsing)
                probability = self._extract_probability_from_analysis(analysis)
                
                if probability >= similarity_threshold:
                    results.append({
                        "compound_name": name,
                        "smiles": smiles,
                        "analysis": analysis,
                        "probability": probability,
                        "type": "candidate"
                    })
        
        # Sort by probability
        candidates = [r for r in results if r.get("type") == "candidate"]
        candidates.sort(key=lambda x: x.get("probability", 0), reverse=True)
        
        return [r for r in results if r.get("type") == "reference"] + candidates
    
    def _extract_probability_from_analysis(self, analysis: str) -> float:
        """Extract probability value from analysis text"""
        try:
            # Simple regex to find probability value
            import re
            match = re.search(r'Probability: (\d+\.\d+)', analysis)
            if match:
                return float(match.group(1))
        except:
            pass
        return 0.0
    
    def generate_compound_discovery_report(self, query: str, target_properties: Dict[str, Any] = None) -> str:
        """
        Generate a comprehensive compound discovery report
        
        Args:
            query: Natural language query (e.g., "Find molecules similar to metformin")
            target_properties: Dict of desired molecular properties
            
        Returns:
            Formatted discovery report
        """
        report = f"🧪 **Compound Discovery Report**\n"
        report += f"Query: {query}\n"
        report += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # Parse query to identify reference compound
        reference_smiles = None
        for name, smiles in self.compound_database.items():
            if name.lower() in query.lower():
                reference_smiles = smiles
                report += f"Reference Compound Identified: {name}\n"
                break
        
        if not reference_smiles:
            # Default to metformin if no specific compound mentioned
            reference_smiles = self.compound_database["Metformin"]
            report += f"Using default reference: Metformin\n"
        
        report += f"Reference SMILES: {reference_smiles}\n\n"
        
        # Find similar compounds
        similar_compounds = self.find_similar_compounds(reference_smiles, 0.5)
        
        report += f"## 📊 Discovery Results\n"
        report += f"Found {len([c for c in similar_compounds if c.get('type') == 'candidate'])} candidate compounds\n\n"
        
        for compound in similar_compounds:
            if compound.get("type") == "reference":
                report += f"### 🎯 Reference Analysis\n"
            else:
                report += f"### 🧬 {compound['compound_name']}\n"
            
            report += f"SMILES: `{compound['smiles']}`\n"
            if compound.get("probability"):
                report += f"Lifespan Extension Probability: {compound['probability']:.3f}\n"
            report += f"\n{compound['analysis']}\n\n"
        
        # Recommendations
        report += f"## 💡 Research Recommendations\n"
        high_potential = [c for c in similar_compounds if c.get("probability", 0) > 0.7]
        
        if high_potential:
            report += f"- **High Priority Candidates**: {len(high_potential)} compounds show strong potential\n"
            for compound in high_potential[:3]:  # Top 3
                report += f"  - {compound['compound_name']}: {compound.get('probability', 0):.1%} probability\n"
        
        report += f"- Consider in vitro studies for top candidates\n"
        report += f"- Investigate molecular mechanisms of action\n"
        report += f"- Review existing literature for safety profiles\n"
        
        return report

class HealthAssessmentAgent:
    """
    Intelligent agent for comprehensive health assessment
    Combines multiple LifeMindML modules for holistic health analysis
    """
    
    def __init__(self, tool_registry: ToolRegistry):
        self.tool_registry = tool_registry
        self.lifestyle_tool = tool_registry.get_tool_by_name("lifestyle_optimizer")
        self.survival_tool = tool_registry.get_tool_by_name("survival_analyzer")
        self.disease_tool = tool_registry.get_tool_by_name("disease_detector")
        self.voice_tool = tool_registry.get_tool_by_name("voice_emotion_analyzer")
        self.comprehensive_tool = tool_registry.get_tool_by_name("comprehensive_health_assessor")
    
    def analyze_patient_profile(self, patient_data: Dict[str, Any]) -> str:
        """
        Analyze comprehensive patient profile combining multiple data sources
        
        Args:
            patient_data: Dict containing lifestyle, clinical, and other health data
            
        Returns:
            Comprehensive health assessment report
        """
        report = f"🏥 **Comprehensive Health Assessment**\n"
        report += f"Patient ID: {patient_data.get('patient_id', 'Anonymous')}\n"
        report += f"Assessment Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        assessment_modules = []
        
        # Lifestyle Assessment
        if 'lifestyle' in patient_data and self.lifestyle_tool:
            lifestyle_data = patient_data['lifestyle']
            if all(key in lifestyle_data for key in ['sleep_hours', 'steps', 'calories', 'protein']):
                lifestyle_result = self.lifestyle_tool["function"](**lifestyle_data)
                report += f"## 🧘 Lifestyle Analysis\n{lifestyle_result}\n\n"
                assessment_modules.append("Lifestyle")
        
        # Clinical Risk Assessment
        if 'clinical' in patient_data and self.survival_tool:
            clinical_data = patient_data['clinical']
            required_fields = ['age', 'ejection_fraction', 'serum_creatinine', 'serum_sodium']
            if all(key in clinical_data for key in required_fields):
                clinical_result = self.survival_tool["function"](**clinical_data)
                report += f"## 📈 Clinical Risk Assessment\n{clinical_result}\n\n"
                assessment_modules.append("Clinical")
        
        # Medical Imaging Analysis
        if 'medical_images' in patient_data and self.disease_tool:
            for i, image_data in enumerate(patient_data['medical_images']):
                image_result = self.disease_tool["function"](image_data)
                report += f"## 🩻 Medical Image Analysis {i+1}\n{image_result}\n\n"
                assessment_modules.append("Imaging")
        
        # Voice/Mental Health Analysis
        if 'voice_data' in patient_data and self.voice_tool:
            voice_result = self.voice_tool["function"](patient_data['voice_data'])
            report += f"## 🧏 Mental Health Assessment\n{voice_result}\n\n"
            assessment_modules.append("Mental Health")
        
        # Integrated Assessment Summary
        report += f"## 📊 Integrated Health Summary\n"
        report += f"Assessment Modules Completed: {', '.join(assessment_modules)}\n"
        
        # Generate overall recommendations
        report += f"\n## 💡 Integrated Recommendations\n"
        report += f"Based on the multi-modal assessment:\n"
        report += f"- Continue regular health monitoring across all assessed domains\n"
        report += f"- Address any identified risk factors through targeted interventions\n"
        report += f"- Consider preventive measures based on predictive insights\n"
        report += f"- Schedule appropriate follow-up assessments\n"
        
        return report
    
    def generate_intervention_recommendations(self, assessment_results: Dict[str, Any]) -> List[str]:
        """
        Generate personalized intervention recommendations based on assessment results
        
        Args:
            assessment_results: Results from comprehensive health assessment
            
        Returns:
            List of actionable intervention recommendations
        """
        recommendations = []
        
        # Lifestyle interventions
        if 'lifestyle_score' in assessment_results:
            score = assessment_results['lifestyle_score']
            if score < 70:
                recommendations.extend([
                    "Implement structured sleep hygiene program",
                    "Increase daily physical activity with gradual progression",
                    "Consult with nutritionist for personalized meal planning",
                    "Consider stress management techniques (meditation, yoga)"
                ])
        
        # Clinical interventions
        if 'clinical_risk' in assessment_results:
            risk = assessment_results['clinical_risk']
            if risk == "High":
                recommendations.extend([
                    "Immediate cardiology consultation recommended",
                    "Optimize medication regimen under medical supervision",
                    "Implement intensive monitoring protocol",
                    "Consider cardiac rehabilitation program"
                ])
        
        # Mental health interventions
        if 'mental_health_concerns' in assessment_results:
            recommendations.extend([
                "Consider mental health screening with qualified professional",
                "Implement stress reduction strategies",
                "Maintain social connections and support networks",
                "Consider mindfulness-based interventions"
            ])
        
        return recommendations

class LongevityResearchAgent:
    """
    Intelligent agent for longevity research and intervention comparison
    Specializes in analyzing survival outcomes and research strategies
    """
    
    def __init__(self, tool_registry: ToolRegistry):
        self.tool_registry = tool_registry
        self.survival_tool = tool_registry.get_tool_by_name("survival_analyzer")
        self.drug_tool = tool_registry.get_tool_by_name("lifespan_drug_predictor")
        self.lifestyle_tool = tool_registry.get_tool_by_name("lifestyle_optimizer")
    
    def compare_intervention_strategies(self, baseline_profile: Dict[str, Any], 
                                      interventions: List[Dict[str, Any]]) -> str:
        """
        Compare survival outcomes for different intervention strategies
        
        Args:
            baseline_profile: Baseline patient profile
            interventions: List of intervention scenarios to compare
            
        Returns:
            Comparative analysis report
        """
        report = f"📈 **Longevity Intervention Comparison Study**\n"
        report += f"Study Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Baseline Profile: Age {baseline_profile.get('age', 'Unknown')}\n\n"
        
        # Analyze baseline
        if self.survival_tool:
            baseline_result = self.survival_tool["function"](**baseline_profile)
            report += f"## 🎯 Baseline Analysis\n{baseline_result}\n\n"
        
        # Compare interventions
        report += f"## 🔬 Intervention Comparisons\n"
        
        intervention_results = []
        
        for i, intervention in enumerate(interventions, 1):
            report += f"### Intervention {i}: {intervention.get('name', f'Strategy {i}')}\n"
            
            # Apply intervention to baseline profile
            modified_profile = baseline_profile.copy()
            
            # Apply modifications based on intervention type
            if intervention.get('type') == 'lifestyle':
                # Lifestyle intervention
                lifestyle_changes = intervention.get('changes', {})
                for key, value in lifestyle_changes.items():
                    if key in modified_profile:
                        modified_profile[key] = value
            
            elif intervention.get('type') == 'clinical':
                # Clinical intervention
                clinical_changes = intervention.get('changes', {})
                for key, value in clinical_changes.items():
                    if key in modified_profile:
                        modified_profile[key] = value
            
            # Analyze modified profile
            if self.survival_tool:
                intervention_result = self.survival_tool["function"](**modified_profile)
                report += f"{intervention_result}\n\n"
                
                # Extract survival probability for comparison
                survival_prob = self._extract_survival_probability(intervention_result)
                intervention_results.append({
                    'name': intervention.get('name', f'Strategy {i}'),
                    'survival_probability': survival_prob,
                    'type': intervention.get('type', 'unknown')
                })
        
        # Comparative summary
        if intervention_results:
            report += f"## 📊 Comparative Summary\n"
            intervention_results.sort(key=lambda x: x['survival_probability'], reverse=True)
            
            report += f"**Intervention Ranking (by survival probability):**\n"
            for i, result in enumerate(intervention_results, 1):
                report += f"{i}. {result['name']}: {result['survival_probability']:.1%} survival probability\n"
            
            # Best intervention recommendation
            best_intervention = intervention_results[0]
            report += f"\n**Recommended Strategy**: {best_intervention['name']}\n"
            report += f"Expected improvement: {best_intervention['survival_probability']:.1%} survival probability\n"
        
        return report
    
    def _extract_survival_probability(self, analysis: str) -> float:
        """Extract survival probability from analysis text"""
        try:
            import re
            match = re.search(r'(\d+)-Day Survival Probability: (\d+\.\d+)%', analysis)
            if match:
                return float(match.group(2)) / 100
        except:
            pass
        return 0.0
    
    def generate_research_hypothesis(self, research_question: str) -> str:
        """
        Generate testable research hypotheses based on research questions
        
        Args:
            research_question: Natural language research question
            
        Returns:
            Structured research hypothesis with methodology suggestions
        """
        report = f"🔬 **Research Hypothesis Generation**\n"
        report += f"Research Question: {research_question}\n"
        report += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # Analyze research question to generate relevant hypotheses
        if "compound" in research_question.lower() or "drug" in research_question.lower():
            report += f"## 🧪 Compound Research Hypothesis\n"
            report += f"**Primary Hypothesis**: Novel compounds with specific molecular features will demonstrate significant lifespan extension effects in model organisms.\n\n"
            report += f"**Methodology Suggestions**:\n"
            report += f"- Screen compound libraries using LifeMindML drug classifier\n"
            report += f"- Validate top candidates in C. elegans or Drosophila models\n"
            report += f"- Analyze molecular mechanisms through pathway analysis\n"
            report += f"- Conduct dose-response studies for promising compounds\n\n"
        
        elif "lifestyle" in research_question.lower() or "intervention" in research_question.lower():
            report += f"## 🧘 Lifestyle Intervention Hypothesis\n"
            report += f"**Primary Hypothesis**: Personalized lifestyle interventions based on biological age prediction will result in measurable improvements in healthspan markers.\n\n"
            report += f"**Methodology Suggestions**:\n"
            report += f"- Recruit participants with elevated biological age\n"
            report += f"- Implement personalized intervention protocols\n"
            report += f"- Monitor biomarkers and biological age changes\n"
            report += f"- Compare outcomes with control group\n\n"
        
        elif "survival" in research_question.lower() or "longevity" in research_question.lower():
            report += f"## 📈 Longevity Research Hypothesis\n"
            report += f"**Primary Hypothesis**: Multi-modal health assessment can predict longevity outcomes with higher accuracy than single-factor models.\n\n"
            report += f"**Methodology Suggestions**:\n"
            report += f"- Collect comprehensive health data from cohort\n"
            report += f"- Apply LifeMindML multi-modal analysis\n"
            report += f"- Follow participants longitudinally\n"
            report += f"- Validate predictive accuracy against outcomes\n\n"
        
        # General research recommendations
        report += f"## 💡 General Research Recommendations\n"
        report += f"- Ensure adequate sample size for statistical power\n"
        report += f"- Include appropriate control groups\n"
        report += f"- Consider ethical implications and obtain proper approvals\n"
        report += f"- Plan for data sharing and reproducibility\n"
        report += f"- Integrate with existing biomedical databases\n"
        
        return report

# Agent Registry
LIFEMIND_AGENTS = {
    "compound_discovery": CompoundDiscoveryAgent,
    "health_assessment": HealthAssessmentAgent,
    "longevity_research": LongevityResearchAgent
}
