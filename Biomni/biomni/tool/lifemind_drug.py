import sys
import os

# 🔧 Add project root to sys.path (../ from /Biomni/biomni/)
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

from src.drug_model import predict_lifespan_effect

LifespanDrugClassifierTool = {
    "name": "lifespan_drug_predictor",
    "description": "Predicts the probability of a molecule extending lifespan using LifeMind drug model.",
    "required_parameters": {
        "smiles": "Molecular SMILES string"
    },
    "function": lambda smiles: f"🧪 Predicted lifespan extension probability: {predict_lifespan_effect(smiles):.2f}"
}
