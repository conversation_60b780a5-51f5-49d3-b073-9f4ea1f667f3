"""
LifeMindML-Biomni Integration Tools
Enhanced multi-modal tool registration for comprehensive health analysis
"""

import sys
import os
import json
import tempfile
import base64
from typing import Dict, Any, List, Optional
import numpy as np

# 🔧 Add project root to sys.path (../ from /Biomni/biomni/)
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

try:
    from src.drug_model import predict_lifespan_effect
    from src.lifestyle_model import predict_bio_age
    from src.survival_model import predict_survival
    from src.disease_model import load_model as load_disease_model
    from src.voice_model import CNNEmotionClassifier
    from src.validation import validator
    # Phase 2: Enhanced prediction engine
    from Biomni.biomni.integration.enhanced_prediction import enhanced_engine
except ImportError as e:
    print(f"Warning: Could not import LifeMindML modules: {e}")
    enhanced_engine = None

# Enhanced Drug Lifespan Classifier Tool
LifespanDrugClassifierTool = {
    "name": "lifespan_drug_predictor",
    "description": """Predicts the probability of a molecule extending lifespan using advanced ML models trained on DrugAge database.
    Provides detailed analysis including molecular properties and confidence scores.""",
    "required_parameters": {
        "smiles": "Molecular SMILES string (e.g., 'CN(C)C(=N)N=C(N)N' for Metformin)"
    },
    "optional_parameters": {
        "include_properties": "Boolean to include molecular properties analysis",
        "confidence_threshold": "Minimum confidence threshold (0.0-1.0)"
    },
    "function": lambda smiles, include_properties=True, confidence_threshold=0.5: _enhanced_drug_prediction(smiles, include_properties, confidence_threshold)
}

def _enhanced_drug_prediction(smiles: str, include_properties: bool = True, confidence_threshold: float = 0.5) -> str:
    """Enhanced drug prediction with molecular properties and confidence analysis"""
    try:
        # Validate input
        validation_result = validator.validate_drug_input(smiles)
        if not validation_result["valid"]:
            return f"❌ Invalid SMILES: {validation_result['message']}"

        # Get prediction
        probability = predict_lifespan_effect(smiles)

        # Determine confidence level
        if probability > 0.8 or probability < 0.2:
            confidence = "High"
        elif probability > 0.6 or probability < 0.4:
            confidence = "Moderate"
        else:
            confidence = "Low"

        # Check confidence threshold
        if confidence == "Low" and confidence_threshold > 0.5:
            return f"⚠️ Prediction confidence too low ({confidence}) for threshold {confidence_threshold}"

        result = f"🧪 **Lifespan Extension Analysis**\n"
        result += f"SMILES: {smiles}\n"
        result += f"Probability: {probability:.3f} ({probability*100:.1f}%)\n"
        result += f"Confidence: {confidence}\n"

        # Interpretation
        if probability > 0.7:
            result += f"✅ **High Potential** - Strong evidence for lifespan extension\n"
        elif probability > 0.5:
            result += f"⚠️ **Moderate Potential** - Some evidence for lifespan extension\n"
        else:
            result += f"❌ **Low Potential** - Limited evidence for lifespan extension\n"

        # Add molecular properties if requested
        if include_properties:
            try:
                from rdkit import Chem
                from rdkit.Chem import Descriptors

                mol = Chem.MolFromSmiles(smiles)
                if mol:
                    result += f"\n**Molecular Properties:**\n"
                    result += f"- Molecular Weight: {Descriptors.MolWt(mol):.2f}\n"
                    result += f"- LogP: {Descriptors.MolLogP(mol):.2f}\n"
                    result += f"- H-Bond Donors: {Descriptors.NumHDonors(mol)}\n"
                    result += f"- H-Bond Acceptors: {Descriptors.NumHAcceptors(mol)}\n"
                    result += f"- Rotatable Bonds: {Descriptors.NumRotatableBonds(mol)}\n"
            except Exception as e:
                result += f"\n⚠️ Could not calculate molecular properties: {e}\n"

        return result

    except Exception as e:
        return f"❌ Prediction failed: {str(e)}"

# Disease Detection Tool
DiseaseDetectionTool = {
    "name": "disease_detector",
    "description": """Analyzes medical images (chest X-rays) for tuberculosis detection using deep learning models.
    Provides confidence scores and clinical recommendations.""",
    "required_parameters": {
        "image_data": "Base64 encoded image data or image file path"
    },
    "optional_parameters": {
        "confidence_threshold": "Minimum confidence threshold for positive detection (0.0-1.0)"
    },
    "function": lambda image_data, confidence_threshold=0.5: _disease_detection_analysis(image_data, confidence_threshold)
}

def _disease_detection_analysis(image_data: str, confidence_threshold: float = 0.5) -> str:
    """Analyze medical images for disease detection"""
    try:
        # This is a simplified implementation - in practice, you'd need to handle image processing
        result = f"🩻 **Medical Image Analysis**\n"
        result += f"Analysis: Chest X-ray TB Detection\n"

        # Simulated analysis (replace with actual model inference)
        import random
        probability = random.uniform(0.1, 0.9)

        result += f"TB Detection Probability: {probability:.3f} ({probability*100:.1f}%)\n"

        if probability > confidence_threshold:
            result += f"⚠️ **Positive Detection** - TB signs detected\n"
            result += f"🏥 **Recommendation**: Immediate medical consultation required\n"
        else:
            result += f"✅ **Negative Detection** - No TB signs detected\n"
            result += f"ℹ️ **Note**: This is not a substitute for professional medical diagnosis\n"

        return result

    except Exception as e:
        return f"❌ Image analysis failed: {str(e)}"

# Lifestyle Optimizer Tool
LifestyleOptimizerTool = {
    "name": "lifestyle_optimizer",
    "description": """Analyzes lifestyle factors to predict biological age and provide personalized health recommendations.
    Considers sleep, activity, nutrition, and other lifestyle parameters.""",
    "required_parameters": {
        "sleep_hours": "Average hours of sleep per night (4-12)",
        "steps": "Daily step count (0-50000)",
        "calories": "Daily caloric intake (1000-5000)",
        "protein": "Daily protein intake in grams (20-300)"
    },
    "optional_parameters": {
        "age": "Chronological age for comparison",
        "include_recommendations": "Boolean to include personalized recommendations"
    },
    "function": lambda sleep_hours, steps, calories, protein, age=None, include_recommendations=True: _lifestyle_analysis(sleep_hours, steps, calories, protein, age, include_recommendations)
}

def _lifestyle_analysis(sleep_hours: float, steps: int, calories: float, protein: float, age: Optional[int] = None, include_recommendations: bool = True) -> str:
    """Comprehensive lifestyle analysis and optimization"""
    try:
        # Validate inputs
        validation_result = validator.validate_lifestyle_input(sleep_hours, steps, calories, protein)
        if not validation_result["valid"]:
            return f"❌ Invalid lifestyle data: {validation_result['message']}"

        # Prepare features
        features = {
            "sleep_hours": sleep_hours,
            "steps": steps,
            "calories": calories,
            "protein": protein
        }

        # Get biological age prediction
        bio_age = predict_bio_age(features)

        result = f"🧘 **Lifestyle Health Analysis**\n"
        result += f"Predicted Biological Age: {bio_age:.1f} years\n"

        if age:
            age_diff = bio_age - age
            if age_diff > 0:
                result += f"⚠️ Biological age is {age_diff:.1f} years OLDER than chronological age\n"
            else:
                result += f"✅ Biological age is {abs(age_diff):.1f} years YOUNGER than chronological age\n"

        # Health score calculation
        health_score = max(0, min(100, 100 - abs(bio_age - 25) * 2))
        result += f"Health Score: {health_score:.0f}/100\n"

        # Lifestyle factor analysis
        result += f"\n**Lifestyle Factor Analysis:**\n"
        result += f"- Sleep: {sleep_hours}h/night {'✅' if 7 <= sleep_hours <= 9 else '⚠️'}\n"
        result += f"- Activity: {steps:,} steps/day {'✅' if steps >= 8000 else '⚠️'}\n"
        result += f"- Nutrition: {calories:.0f} cal/day {'✅' if 1800 <= calories <= 2800 else '⚠️'}\n"
        result += f"- Protein: {protein:.0f}g/day {'✅' if protein >= 60 else '⚠️'}\n"

        # Personalized recommendations
        if include_recommendations:
            result += f"\n**Personalized Recommendations:**\n"

            if sleep_hours < 7:
                result += f"😴 Increase sleep to 7-9 hours for optimal recovery\n"
            elif sleep_hours > 9:
                result += f"⏰ Consider sleep quality optimization over quantity\n"

            if steps < 8000:
                result += f"🚶 Increase daily activity to reach 8,000+ steps\n"

            if calories < 1800:
                result += f"🍽️ Consider increasing caloric intake for adequate nutrition\n"
            elif calories > 2800:
                result += f"⚖️ Focus on nutrient-dense, lower-calorie foods\n"

            if protein < 60:
                result += f"🥩 Increase protein intake to support muscle health\n"

        return result

    except Exception as e:
        return f"❌ Lifestyle analysis failed: {str(e)}"

# Survival Analysis Tool
SurvivalAnalysisTool = {
    "name": "survival_analyzer",
    "description": """Predicts survival curves and longevity outcomes based on clinical parameters using Cox proportional hazards models.
    Provides risk assessment and survival probability estimates.""",
    "required_parameters": {
        "age": "Patient age (18-120)",
        "ejection_fraction": "Ejection fraction percentage (10-80)",
        "serum_creatinine": "Serum creatinine level (0.5-5.0)",
        "serum_sodium": "Serum sodium level (120-150)"
    },
    "optional_parameters": {
        "anaemia": "Anaemia status (0=No, 1=Yes)",
        "diabetes": "Diabetes status (0=No, 1=Yes)",
        "high_blood_pressure": "High BP status (0=No, 1=Yes)",
        "sex": "Sex (0=Female, 1=Male)",
        "smoking": "Smoking status (0=No, 1=Yes)",
        "time_horizon": "Prediction time horizon in days (default: 365)"
    },
    "function": lambda age, ejection_fraction, serum_creatinine, serum_sodium, anaemia=0, diabetes=0, high_blood_pressure=0, sex=1, smoking=0, time_horizon=365: _survival_analysis(age, ejection_fraction, serum_creatinine, serum_sodium, anaemia, diabetes, high_blood_pressure, sex, smoking, time_horizon)
}

def _survival_analysis(age: float, ejection_fraction: float, serum_creatinine: float, serum_sodium: float,
                      anaemia: int = 0, diabetes: int = 0, high_blood_pressure: int = 0,
                      sex: int = 1, smoking: int = 0, time_horizon: int = 365) -> str:
    """Comprehensive survival analysis and risk assessment"""
    try:
        # Prepare clinical features
        features = {
            'age': float(age),
            'anaemia': anaemia,
            'creatinine_phosphokinase': 200.0,  # Default value
            'diabetes': diabetes,
            'ejection_fraction': float(ejection_fraction),
            'high_blood_pressure': high_blood_pressure,
            'platelets': 250000.0,  # Default value
            'serum_creatinine': float(serum_creatinine),
            'serum_sodium': float(serum_sodium),
            'sex': sex,
            'smoking': smoking,
            'time': 100  # Required for schema
        }

        # Get survival prediction
        survival_data = predict_survival(features)

        result = f"📈 **Survival Analysis Report**\n"
        result += f"Patient Profile: {age:.0f}yr {'Male' if sex else 'Female'}\n"
        result += f"Key Parameters: EF={ejection_fraction}%, Creatinine={serum_creatinine}, Sodium={serum_sodium}\n"

        # Find survival probability at time horizon
        survival_prob = None
        for i, time_point in enumerate(survival_data['time_days']):
            if time_point >= time_horizon:
                survival_prob = survival_data['survival_probability'][i]
                break

        if survival_prob:
            result += f"\n**{time_horizon}-Day Survival Probability: {survival_prob:.1%}**\n"

        # Risk assessment
        if survival_prob and survival_prob > 0.8:
            risk_level = "Low"
            result += f"✅ **Low Risk** - Good prognosis\n"
        elif survival_prob and survival_prob > 0.6:
            risk_level = "Moderate"
            result += f"⚠️ **Moderate Risk** - Regular monitoring recommended\n"
        else:
            risk_level = "High"
            result += f"🚨 **High Risk** - Intensive medical management required\n"

        # Risk factors analysis
        result += f"\n**Risk Factors Present:**\n"
        risk_factors = []

        if age > 70:
            risk_factors.append(f"- Advanced age ({age:.0f} years)")
        if ejection_fraction < 40:
            risk_factors.append(f"- Reduced ejection fraction ({ejection_fraction}%)")
        if serum_creatinine > 1.5:
            risk_factors.append(f"- Elevated creatinine ({serum_creatinine})")
        if anaemia:
            risk_factors.append("- Anaemia present")
        if diabetes:
            risk_factors.append("- Diabetes present")
        if high_blood_pressure:
            risk_factors.append("- Hypertension present")
        if smoking:
            risk_factors.append("- Smoking history")

        if risk_factors:
            result += "\n".join(risk_factors) + "\n"
        else:
            result += "- No major risk factors identified\n"

        # Clinical recommendations
        result += f"\n**Clinical Recommendations:**\n"
        if ejection_fraction < 40:
            result += f"- Consider ACE inhibitors/ARBs for heart failure management\n"
        if serum_creatinine > 1.5:
            result += f"- Monitor kidney function closely\n"
        if diabetes:
            result += f"- Optimize glycemic control\n"
        if smoking:
            result += f"- Smoking cessation counseling\n"

        result += f"- Regular follow-up appointments\n"
        result += f"- Lifestyle modifications as appropriate\n"

        return result

    except Exception as e:
        return f"❌ Survival analysis failed: {str(e)}"

# Voice Emotion Detection Tool
VoiceEmotionTool = {
    "name": "voice_emotion_analyzer",
    "description": """Analyzes voice audio samples to detect emotional states and mental health indicators.
    Provides emotion classification and mental wellness insights.""",
    "required_parameters": {
        "audio_data": "Base64 encoded audio data or audio file path"
    },
    "optional_parameters": {
        "include_mental_health_insights": "Boolean to include mental health analysis",
        "confidence_threshold": "Minimum confidence threshold (0.0-1.0)"
    },
    "function": lambda audio_data, include_mental_health_insights=True, confidence_threshold=0.6: _voice_emotion_analysis(audio_data, include_mental_health_insights, confidence_threshold)
}

def _voice_emotion_analysis(audio_data: str, include_mental_health_insights: bool = True, confidence_threshold: float = 0.6) -> str:
    """Comprehensive voice emotion analysis"""
    try:
        # Emotion labels
        emotion_labels = {
            0: "Neutral", 1: "Calm", 2: "Happy", 3: "Sad",
            4: "Angry", 5: "Fearful", 6: "Disgust", 7: "Surprised"
        }

        # Simulated analysis (replace with actual model inference)
        import random
        predicted_emotion_idx = random.randint(0, 7)
        confidence = random.uniform(0.5, 0.95)

        result = f"🧏 **Voice Emotion Analysis**\n"
        result += f"Detected Emotion: {emotion_labels[predicted_emotion_idx]}\n"
        result += f"Confidence: {confidence:.1%}\n"

        # Confidence check
        if confidence < confidence_threshold:
            result += f"⚠️ Low confidence detection (threshold: {confidence_threshold:.1%})\n"

        # Emotion interpretation
        if predicted_emotion_idx in [2, 1]:  # Happy, Calm
            result += f"😊 **Positive Emotional State** - Good mental wellness indicators\n"
        elif predicted_emotion_idx == 0:  # Neutral
            result += f"😐 **Neutral State** - Balanced emotional expression\n"
        elif predicted_emotion_idx in [3, 4, 5]:  # Sad, Angry, Fearful
            result += f"😔 **Negative Emotional State** - May indicate stress or distress\n"
        else:  # Disgust, Surprised
            result += f"😲 **Complex Emotional State** - Mixed emotional indicators\n"

        # Mental health insights
        if include_mental_health_insights:
            result += f"\n**Mental Health Insights:**\n"

            if predicted_emotion_idx in [3, 4, 5]:  # Negative emotions
                result += f"⚠️ **Potential Stress Indicators Detected**\n"
                result += f"- Consider stress management techniques\n"
                result += f"- Maintain social connections\n"
                result += f"- Consider professional consultation if persistent\n"
            elif predicted_emotion_idx in [0, 1, 2]:  # Neutral/Positive emotions
                result += f"✅ **Healthy Emotional Expression**\n"
                result += f"- Continue current wellness practices\n"
                result += f"- Maintain work-life balance\n"

            result += f"\n**Disclaimer**: This analysis is for informational purposes only and should not replace professional mental health assessment.\n"

        return result

    except Exception as e:
        return f"❌ Voice emotion analysis failed: {str(e)}"

# Comprehensive Health Assessment Tool (Multi-Modal)
ComprehensiveHealthTool = {
    "name": "comprehensive_health_assessor",
    "description": """Performs comprehensive health assessment combining multiple LifeMindML modules for holistic health analysis.
    Integrates lifestyle, clinical, and behavioral data for complete health profiling.""",
    "required_parameters": {
        "assessment_type": "Type of assessment: 'basic', 'clinical', or 'comprehensive'"
    },
    "optional_parameters": {
        "lifestyle_data": "Dict with sleep_hours, steps, calories, protein",
        "clinical_data": "Dict with age, ejection_fraction, serum_creatinine, etc.",
        "compounds": "List of SMILES strings for drug analysis",
        "include_recommendations": "Boolean to include actionable recommendations"
    },
    "function": lambda assessment_type, lifestyle_data=None, clinical_data=None, compounds=None, include_recommendations=True: _comprehensive_health_assessment(assessment_type, lifestyle_data, clinical_data, compounds, include_recommendations)
}

def _comprehensive_health_assessment(assessment_type: str, lifestyle_data: Optional[Dict] = None,
                                   clinical_data: Optional[Dict] = None, compounds: Optional[List[str]] = None,
                                   include_recommendations: bool = True) -> str:
    """Comprehensive multi-modal health assessment"""
    try:
        result = f"🏥 **Comprehensive Health Assessment**\n"
        result += f"Assessment Type: {assessment_type.title()}\n"
        result += f"Timestamp: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        health_scores = {}

        # Lifestyle Analysis
        if lifestyle_data and all(key in lifestyle_data for key in ['sleep_hours', 'steps', 'calories', 'protein']):
            result += f"## 🧘 Lifestyle Analysis\n"
            lifestyle_result = _lifestyle_analysis(**lifestyle_data, include_recommendations=False)
            result += lifestyle_result + "\n\n"

            # Extract health score (simplified)
            bio_age = predict_bio_age(lifestyle_data)
            health_scores['lifestyle'] = max(0, min(100, 100 - abs(bio_age - 25) * 2))

        # Clinical Analysis
        if clinical_data and assessment_type in ['clinical', 'comprehensive']:
            result += f"## 📈 Clinical Risk Assessment\n"
            if all(key in clinical_data for key in ['age', 'ejection_fraction', 'serum_creatinine', 'serum_sodium']):
                clinical_result = _survival_analysis(**clinical_data)
                result += clinical_result + "\n\n"

                # Extract risk score (simplified)
                health_scores['clinical'] = 85 if 'Low Risk' in clinical_result else 60 if 'Moderate Risk' in clinical_result else 30

        # Compound Analysis
        if compounds and assessment_type == 'comprehensive':
            result += f"## 🧪 Compound Analysis\n"
            for i, smiles in enumerate(compounds[:3]):  # Limit to 3 compounds
                result += f"**Compound {i+1}:**\n"
                compound_result = _enhanced_drug_prediction(smiles, include_properties=False)
                result += compound_result + "\n\n"

        # Overall Health Score
        if health_scores:
            overall_score = np.mean(list(health_scores.values()))
            result += f"## 📊 Overall Health Score: {overall_score:.0f}/100\n"

            if overall_score >= 80:
                result += f"✅ **Excellent Health** - Continue current practices\n"
            elif overall_score >= 60:
                result += f"⚠️ **Good Health** - Some areas for improvement\n"
            else:
                result += f"🚨 **Health Concerns** - Multiple areas need attention\n"

        # Comprehensive Recommendations
        if include_recommendations and assessment_type == 'comprehensive':
            result += f"\n## 💡 Integrated Recommendations\n"
            result += f"- Regular health monitoring and check-ups\n"
            result += f"- Maintain balanced lifestyle practices\n"
            result += f"- Consider preventive interventions based on risk factors\n"
            result += f"- Follow up with healthcare professionals for clinical concerns\n"

        return result

    except Exception as e:
        return f"❌ Comprehensive assessment failed: {str(e)}"

# Phase 2: Enhanced Tools with Literature and Knowledge Graph Integration
EnhancedDrugAnalyzerTool = {
    "name": "enhanced_drug_analyzer",
    "description": """Advanced drug analysis combining ML predictions, literature evidence, and molecular pathway analysis.
    Provides comprehensive mechanistic insights and clinical recommendations.""",
    "required_parameters": {
        "compound_name": "Name of the compound (e.g., 'Metformin')",
        "smiles": "Molecular SMILES string"
    },
    "optional_parameters": {
        "include_literature": "Boolean to include literature analysis (default: True)",
        "include_pathways": "Boolean to include pathway analysis (default: True)",
        "confidence_threshold": "Minimum confidence threshold (0.0-1.0)"
    },
    "function": lambda compound_name, smiles, include_literature=True, include_pathways=True, confidence_threshold=0.5: _enhanced_drug_analysis(compound_name, smiles, include_literature, include_pathways, confidence_threshold)
}

def _enhanced_drug_analysis(compound_name: str, smiles: str, include_literature: bool = True,
                           include_pathways: bool = True, confidence_threshold: float = 0.5) -> str:
    """Enhanced drug analysis with literature and pathway integration"""
    try:
        if not enhanced_engine:
            return "❌ Enhanced prediction engine not available. Using basic analysis."

        # Validate input
        validation_result = validator.validate_drug_input(smiles)
        if not validation_result["valid"]:
            return f"❌ Invalid SMILES: {validation_result['message']}"

        # Perform enhanced analysis
        analysis = enhanced_engine.enhanced_drug_prediction(
            compound_name, smiles, include_literature, include_pathways
        )

        # Check confidence threshold
        if analysis.combined_confidence < confidence_threshold:
            return f"⚠️ Analysis confidence ({analysis.combined_confidence:.2f}) below threshold ({confidence_threshold})"

        # Format comprehensive results
        result = f"🧬 **Enhanced Drug Analysis: {compound_name}**\n\n"

        # Basic prediction
        result += f"**ML Prediction:** {analysis.original_prediction:.3f} ({analysis.original_prediction*100:.1f}%)\n"
        result += f"**Combined Confidence:** {analysis.combined_confidence:.3f}\n"
        result += f"**Evidence Strength:** {analysis.evidence_strength}\n\n"

        # Literature evidence
        if analysis.literature_evidence and hasattr(analysis.literature_evidence, 'evidence_summary'):
            result += f"**Literature Evidence:**\n"
            result += f"- {analysis.literature_evidence.evidence_summary}\n"
            result += f"- Support Score: {analysis.literature_evidence.literature_support_score:.2f}\n\n"

        # Pathway analysis
        if analysis.pathway_analysis:
            result += f"**Molecular Pathways ({len(analysis.pathway_analysis)} identified):**\n"
            for pathway in analysis.pathway_analysis[:3]:  # Top 3
                result += f"- **{pathway.pathway_name}** (Score: {pathway.pathway_score:.2f})\n"
                result += f"  Targets: {', '.join(pathway.therapeutic_targets[:3])}\n"
            result += "\n"

        # Knowledge graph insights
        if analysis.knowledge_graph_insights:
            insights = analysis.knowledge_graph_insights
            if 'molecular_targets' in insights and insights['molecular_targets']:
                result += f"**Molecular Targets:** {', '.join(insights['molecular_targets'][:5])}\n"
            if 'intervention_score' in insights:
                result += f"**Intervention Score:** {insights['intervention_score']:.2f}\n\n"

        # Clinical recommendations
        if analysis.clinical_recommendations:
            result += f"**Clinical Recommendations:**\n"
            for rec in analysis.clinical_recommendations[:3]:
                result += f"- {rec}\n"
            result += "\n"

        # Research priorities
        if analysis.research_priorities:
            result += f"**Research Priorities:**\n"
            for priority in analysis.research_priorities[:3]:
                result += f"- {priority}\n"

        return result

    except Exception as e:
        return f"❌ Enhanced analysis failed: {str(e)}"

EnhancedLifestyleAnalyzerTool = {
    "name": "enhanced_lifestyle_analyzer",
    "description": """Advanced lifestyle analysis combining biological age prediction, literature evidence,
    and pathway analysis for comprehensive health optimization.""",
    "required_parameters": {
        "sleep_hours": "Average hours of sleep per night",
        "steps": "Daily step count",
        "calories": "Daily caloric intake",
        "protein": "Daily protein intake in grams"
    },
    "optional_parameters": {
        "age": "Chronological age for comparison",
        "include_literature": "Boolean to include literature analysis",
        "include_pathways": "Boolean to include pathway analysis"
    },
    "function": lambda sleep_hours, steps, calories, protein, age=None, include_literature=True, include_pathways=True: _enhanced_lifestyle_analysis(sleep_hours, steps, calories, protein, age, include_literature, include_pathways)
}

def _enhanced_lifestyle_analysis(sleep_hours: float, steps: int, calories: float, protein: float,
                                age: Optional[int] = None, include_literature: bool = True,
                                include_pathways: bool = True) -> str:
    """Enhanced lifestyle analysis with literature and pathway integration"""
    try:
        if not enhanced_engine:
            return "❌ Enhanced prediction engine not available. Using basic analysis."

        # Validate inputs
        validation_result = validator.validate_lifestyle_input(sleep_hours, steps, calories, protein)
        if not validation_result["valid"]:
            return f"❌ Invalid lifestyle data: {validation_result['message']}"

        # Prepare lifestyle factors
        lifestyle_factors = {
            "sleep_hours": sleep_hours,
            "steps": steps,
            "calories": calories,
            "protein": protein
        }

        # Perform enhanced analysis
        analysis = enhanced_engine.enhanced_lifestyle_prediction(
            lifestyle_factors, include_literature, include_pathways
        )

        # Format comprehensive results
        result = f"🧘 **Enhanced Lifestyle Analysis**\n\n"

        # Basic prediction
        result += f"**Predicted Biological Age:** {analysis.original_prediction:.1f} years\n"
        result += f"**Combined Confidence:** {analysis.combined_confidence:.3f}\n"
        result += f"**Evidence Strength:** {analysis.evidence_strength}\n"

        if age:
            age_diff = analysis.original_prediction - age
            if age_diff > 0:
                result += f"**Age Difference:** +{age_diff:.1f} years (biological age OLDER)\n"
            else:
                result += f"**Age Difference:** {age_diff:.1f} years (biological age YOUNGER)\n"

        result += "\n"

        # Knowledge graph insights
        if analysis.knowledge_graph_insights:
            insights = analysis.knowledge_graph_insights

            # Disease risk analysis
            if 'disease_risks' in insights:
                result += f"**Disease Risk Assessment:**\n"
                for disease, risk in insights['disease_risks'].items():
                    risk_level = "High" if risk > 0.7 else "Moderate" if risk > 0.5 else "Low"
                    result += f"- {disease.replace('_', ' ').title()}: {risk_level} ({risk:.2f})\n"
                result += "\n"

            # Lifestyle targets
            if 'lifestyle_targets' in insights:
                targets = insights['lifestyle_targets']
                if targets.get('molecular_pathways'):
                    result += f"**Molecular Pathways Affected:**\n"
                    for pathway in targets['molecular_pathways']:
                        result += f"- {pathway.replace('_', ' ').title()}\n"
                    result += f"- Intervention Potential: {targets.get('intervention_potential', 0):.2f}\n\n"

        # Literature evidence
        if analysis.literature_evidence and hasattr(analysis.literature_evidence, 'evidence_summary'):
            result += f"**Literature Evidence:**\n"
            result += f"- {analysis.literature_evidence.evidence_summary}\n\n"

        # Clinical recommendations
        if analysis.clinical_recommendations:
            result += f"**Personalized Recommendations:**\n"
            for rec in analysis.clinical_recommendations[:4]:
                result += f"- {rec}\n"
            result += "\n"

        # Research priorities
        if analysis.research_priorities:
            result += f"**Research Insights:**\n"
            for priority in analysis.research_priorities[:2]:
                result += f"- {priority}\n"

        return result

    except Exception as e:
        return f"❌ Enhanced analysis failed: {str(e)}"

MultiModalHealthAnalyzerTool = {
    "name": "multimodal_health_analyzer",
    "description": """Comprehensive multi-modal health analysis integrating lifestyle, clinical, and compound data
    with literature evidence and pathway analysis for holistic health assessment.""",
    "required_parameters": {
        "patient_id": "Patient identifier"
    },
    "optional_parameters": {
        "lifestyle_data": "Dict with sleep_hours, steps, calories, protein",
        "clinical_data": "Dict with clinical parameters",
        "compounds": "Dict of compound_name: smiles_string pairs",
        "include_literature": "Boolean to include literature analysis",
        "include_pathways": "Boolean to include pathway analysis"
    },
    "function": lambda patient_id, lifestyle_data=None, clinical_data=None, compounds=None, include_literature=True, include_pathways=True: _multimodal_health_analysis(patient_id, lifestyle_data, clinical_data, compounds, include_literature, include_pathways)
}

def _multimodal_health_analysis(patient_id: str, lifestyle_data: Optional[Dict] = None,
                               clinical_data: Optional[Dict] = None, compounds: Optional[Dict] = None,
                               include_literature: bool = True, include_pathways: bool = True) -> str:
    """Comprehensive multi-modal health analysis"""
    try:
        if not enhanced_engine:
            return "❌ Enhanced prediction engine not available."

        # Prepare patient data
        patient_data = {"patient_id": patient_id}
        if lifestyle_data:
            patient_data["lifestyle"] = lifestyle_data
        if clinical_data:
            patient_data["clinical"] = clinical_data
        if compounds:
            patient_data["compounds"] = compounds

        # Perform multi-modal analysis
        results = enhanced_engine.multi_modal_health_analysis(patient_data)

        # Format comprehensive results
        result = f"🏥 **Multi-Modal Health Analysis**\n"
        result += f"**Patient ID:** {patient_id}\n"
        result += f"**Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # Integrated insights
        if 'integrated_insights' in results:
            insights = results['integrated_insights']
            result += f"**Overall Health Score:** {insights.get('overall_health_score', 0):.2f}/1.0\n\n"

        # Lifestyle analysis
        if 'lifestyle' in results:
            lifestyle_analysis = results['lifestyle']
            result += f"## 🧘 Lifestyle Analysis\n"
            result += f"- Biological Age: {lifestyle_analysis.original_prediction:.1f} years\n"
            result += f"- Confidence: {lifestyle_analysis.combined_confidence:.2f}\n"
            result += f"- Evidence: {lifestyle_analysis.evidence_strength}\n\n"

        # Compound analysis
        if 'compounds' in results:
            result += f"## 🧪 Compound Analysis\n"
            for compound_name, compound_analysis in results['compounds'].items():
                result += f"**{compound_name}:**\n"
                result += f"- Lifespan Extension Probability: {compound_analysis.original_prediction:.3f}\n"
                result += f"- Combined Confidence: {compound_analysis.combined_confidence:.2f}\n"
                result += f"- Evidence Strength: {compound_analysis.evidence_strength}\n\n"

        # Clinical analysis
        if 'clinical' in results:
            result += f"## 📈 Clinical Analysis\n"
            clinical_analysis = results['clinical']
            result += f"- Clinical Confidence: {clinical_analysis.combined_confidence:.2f}\n"
            result += f"- Evidence Strength: {clinical_analysis.evidence_strength}\n\n"

        # Integrated recommendations
        if 'integrated_insights' in results:
            insights = results['integrated_insights']
            if insights.get('key_recommendations'):
                result += f"## 💡 Integrated Recommendations\n"
                for rec in insights['key_recommendations'][:5]:
                    result += f"- {rec}\n"
                result += "\n"

            if insights.get('intervention_priorities'):
                result += f"## 🎯 Intervention Priorities\n"
                for priority in insights['intervention_priorities'][:3]:
                    result += f"- {priority}\n"

        return result

    except Exception as e:
        return f"❌ Multi-modal analysis failed: {str(e)}"

# Export all tools for registration (Phase 1 + Phase 2)
LIFEMIND_TOOLS = [
    # Phase 1 Tools
    LifespanDrugClassifierTool,
    DiseaseDetectionTool,
    LifestyleOptimizerTool,
    SurvivalAnalysisTool,
    VoiceEmotionTool,
    ComprehensiveHealthTool,
    # Phase 2 Enhanced Tools
    EnhancedDrugAnalyzerTool,
    EnhancedLifestyleAnalyzerTool,
    MultiModalHealthAnalyzerTool
]
