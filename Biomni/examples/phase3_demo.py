"""
Phase 3: Autonomous Research Assistant Demo
Demonstrates autonomous research workflows and personalized health intelligence

This script showcases the advanced capabilities of Phase 3 implementation,
including autonomous research agents, multi-step workflows, and personalized health intelligence.
"""

import sys
import os
import asyncio
from typing import Dict, Any

# Add paths for imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

try:
    from biomni.tool.tool_registry import ToolRegistry
    from biomni.research.workflow_engine import workflow_engine
    from biomni.research.health_intelligence import health_intelligence
    from biomni.research.autonomous_agent import longevity_agent, compound_agent, personalized_agent
except ImportError as e:
    print(f"Error importing Phase 3 modules: {e}")
    print("Please ensure Phase 3 implementation is properly installed")
    sys.exit(1)

def demo_autonomous_research_agent():
    """Demonstrate autonomous research agent capabilities"""
    print("🤖 " + "="*70)
    print("PHASE 3: AUTONOMOUS RESEARCH AGENT DEMONSTRATION")
    print("="*70)
    
    # Test research topics
    research_topics = [
        {
            "topic": "Novel longevity compounds targeting mTOR pathway",
            "agent": compound_agent,
            "domain": "compounds"
        },
        {
            "topic": "Personalized exercise protocols for healthy aging",
            "agent": personalized_agent,
            "domain": "personalized_medicine"
        },
        {
            "topic": "Biomarkers for biological age assessment",
            "agent": longevity_agent,
            "domain": "longevity"
        }
    ]
    
    print("\n📋 Autonomous Research Investigation")
    print("-" * 70)
    
    for research in research_topics:
        print(f"\n🔬 Research Topic: {research['topic']}")
        print("=" * 60)
        
        try:
            agent = research["agent"]
            
            # Step 1: Formulate research question
            print("Step 1: Formulating research question...")
            research_context = f"Investigation of {research['topic']}"
            question_id = agent.formulate_research_question(research_context)
            print(f"✅ Research question formulated: {question_id}")
            
            # Step 2: Generate hypothesis
            print("Step 2: Generating hypothesis...")
            hypothesis_id = agent.generate_hypothesis(question_id)
            hypothesis = agent.hypothesis_registry.get(hypothesis_id)
            
            if hypothesis:
                print(f"✅ Hypothesis generated:")
                print(f"   Text: {hypothesis.hypothesis_text}")
                print(f"   Type: {hypothesis.hypothesis_type.value}")
                print(f"   Confidence: {hypothesis.confidence_score:.2f}")
                print(f"   Priority: {hypothesis.priority.value}")
            
            # Step 3: Design experiment
            print("Step 3: Designing experiment...")
            design_id = agent.design_experiment(hypothesis_id)
            print(f"✅ Experimental design created: {design_id}")
            
            # Step 4: Generate research insights
            print("Step 4: Generating research insights...")
            insight_id = agent.generate_research_insights(
                data_sources=["literature", "knowledge_graph"],
                analysis_focus="patterns"
            )
            insight = agent.insight_database.get(insight_id)
            
            if insight:
                print(f"✅ Research insights generated:")
                print(f"   Insight: {insight.insight_text}")
                print(f"   Confidence: {insight.confidence_level}")
                print(f"   Recommendations: {len(insight.actionable_recommendations)}")
            
            print("\n" + "-" * 60)
            
        except Exception as e:
            print(f"❌ Research investigation failed: {e}")

def demo_personalized_health_intelligence():
    """Demonstrate personalized health intelligence system"""
    print("\n🏥 " + "="*70)
    print("PHASE 3: PERSONALIZED HEALTH INTELLIGENCE DEMONSTRATION")
    print("="*70)
    
    # Test patient profiles
    test_profiles = [
        {
            "name": "Health-Conscious Professional",
            "user_id": "user_001",
            "demographic": {"age": 35, "sex": "M", "height_cm": 175, "weight_kg": 75, "risk_tolerance": "moderate"},
            "lifestyle": {"sleep_hours": 7.5, "steps": 9000, "calories": 2300, "protein": 90},
            "clinical": {"blood_pressure": "120/80", "cholesterol": 180, "glucose": 90},
            "goals": ["longevity", "performance_optimization"]
        },
        {
            "name": "At-Risk Individual",
            "user_id": "user_002", 
            "demographic": {"age": 55, "sex": "F", "height_cm": 165, "weight_kg": 80, "risk_tolerance": "conservative"},
            "lifestyle": {"sleep_hours": 6.0, "steps": 4000, "calories": 2600, "protein": 60},
            "clinical": {"blood_pressure": "140/90", "cholesterol": 220, "glucose": 110},
            "goals": ["disease_prevention", "weight_management"]
        }
    ]
    
    print("\n📋 Personalized Health Intelligence Analysis")
    print("-" * 70)
    
    for profile_data in test_profiles:
        print(f"\n🏥 Profile: {profile_data['name']}")
        print("=" * 50)
        
        try:
            # Create health profile
            print("Step 1: Creating health profile...")
            profile_id = health_intelligence.create_health_profile(
                user_id=profile_data["user_id"],
                demographic_data=profile_data["demographic"],
                lifestyle_data=profile_data["lifestyle"],
                clinical_data=profile_data["clinical"],
                health_goals=profile_data["goals"]
            )
            print(f"✅ Health profile created: {profile_id}")
            
            # Perform comprehensive assessment
            print("Step 2: Performing comprehensive assessment...")
            assessment_id = health_intelligence.perform_comprehensive_assessment(profile_id)
            print(f"✅ Assessment completed: {assessment_id}")
            
            # Generate personalized interventions
            print("Step 3: Generating personalized interventions...")
            intervention_ids = health_intelligence.generate_personalized_interventions(profile_id)
            print(f"✅ Interventions generated: {len(intervention_ids)}")
            
            # Predict health trajectory
            print("Step 4: Predicting health trajectory...")
            trajectory_id = health_intelligence.predict_health_trajectory(profile_id, intervention_ids)
            print(f"✅ Health trajectory predicted: {trajectory_id}")
            
            # Get comprehensive insights
            print("Step 5: Generating health insights...")
            insights = health_intelligence.get_health_insights(profile_id)
            
            # Display key insights
            profile_summary = insights["profile_summary"]
            print(f"\n📊 Key Health Metrics:")
            print(f"   Biological Age: {profile_summary.get('biological_age', 'N/A'):.1f} years")
            print(f"   Health Score: {profile_summary.get('health_score', 'N/A'):.1f}/100")
            
            # Risk priorities
            risk_priorities = insights["risk_priorities"]
            if risk_priorities.get("top_priority"):
                top_risk = risk_priorities["top_priority"]
                print(f"   Top Risk: {top_risk['disease'].replace('_', ' ').title()}")
                print(f"   Risk Score: {top_risk['risk_score']:.2f}")
            
            # Recommendations
            recommendations = insights["personalized_recommendations"]
            if recommendations:
                print(f"\n💡 Top Recommendations:")
                for i, rec in enumerate(recommendations[:3], 1):
                    print(f"   {i}. {rec}")
            
            print("\n" + "-" * 50)
            
        except Exception as e:
            print(f"❌ Health intelligence analysis failed: {e}")

def demo_multistep_research_workflows():
    """Demonstrate multi-step research workflows"""
    print("\n🔬 " + "="*70)
    print("PHASE 3: MULTI-STEP RESEARCH WORKFLOWS DEMONSTRATION")
    print("="*70)
    
    # Test workflow configurations
    workflow_configs = [
        {
            "name": "Compound Discovery Workflow",
            "type": "compound_discovery",
            "parameters": {
                "research_question": "Identify novel compounds for healthy aging",
                "target_properties": {
                    "longevity_focus": True,
                    "safety_profile": "excellent",
                    "bioavailability": "high"
                }
            }
        },
        {
            "name": "Personalized Health Optimization",
            "type": "personalized_health", 
            "parameters": {
                "patient_data": {
                    "patient_id": "workflow_demo_patient",
                    "lifestyle": {"sleep_hours": 7.0, "steps": 8000, "calories": 2200, "protein": 80},
                    "clinical": {"age": 45, "blood_pressure": "130/85"}
                },
                "health_goals": ["longevity", "cardiovascular_health", "metabolic_optimization"]
            }
        }
    ]
    
    print("\n📋 Multi-Step Workflow Creation and Analysis")
    print("-" * 70)
    
    for config in workflow_configs:
        print(f"\n🔬 Workflow: {config['name']}")
        print("=" * 50)
        
        try:
            # Create workflow
            print("Step 1: Creating workflow...")
            if config["type"] == "compound_discovery":
                workflow_id = workflow_engine.create_compound_discovery_workflow(
                    research_question=config["parameters"]["research_question"],
                    target_properties=config["parameters"]["target_properties"]
                )
            else:
                workflow_id = workflow_engine.create_personalized_health_workflow(
                    patient_data=config["parameters"]["patient_data"],
                    health_goals=config["parameters"]["health_goals"]
                )
            
            print(f"✅ Workflow created: {workflow_id}")
            
            # Get workflow definition
            workflow_def = workflow_engine.get_workflow_definition(workflow_id)
            
            if workflow_def:
                print(f"\n📋 Workflow Details:")
                print(f"   Name: {workflow_def.name}")
                print(f"   Description: {workflow_def.description}")
                print(f"   Research Question: {workflow_def.research_question}")
                print(f"   Expected Duration: {workflow_def.expected_duration_minutes} minutes")
                print(f"   Total Steps: {len(workflow_def.steps)}")
                
                print(f"\n🔄 Workflow Steps:")
                for i, step in enumerate(workflow_def.steps, 1):
                    print(f"   {i}. {step.name} ({step.step_type})")
                    print(f"      - {step.description}")
                    print(f"      - Timeout: {step.timeout_minutes} min")
                    print(f"      - Dependencies: {', '.join(step.dependencies) if step.dependencies else 'None'}")
                
                print(f"\n🎯 Success Criteria:")
                for criterion, value in workflow_def.success_criteria.items():
                    print(f"   - {criterion.replace('_', ' ').title()}: {value}")
            
            print("\n" + "-" * 50)
            
        except Exception as e:
            print(f"❌ Workflow creation failed: {e}")

async def demo_autonomous_investigation():
    """Demonstrate autonomous research investigation"""
    print("\n🤖 " + "="*70)
    print("PHASE 3: AUTONOMOUS RESEARCH INVESTIGATION DEMONSTRATION")
    print("="*70)
    
    print("\n📋 Autonomous Investigation Simulation")
    print("-" * 70)
    
    research_topic = "Optimal combination of longevity interventions"
    
    try:
        print(f"🔬 Starting autonomous investigation: {research_topic}")
        
        # Use longevity agent for this investigation
        agent = longevity_agent
        
        # Simulate autonomous investigation
        print("\nStep 1: Autonomous research question formulation...")
        research_context = f"Comprehensive investigation of {research_topic}"
        question_id = agent.formulate_research_question(research_context)
        print(f"✅ Research question formulated")
        
        print("\nStep 2: Autonomous hypothesis generation...")
        hypothesis_id = agent.generate_hypothesis(question_id)
        hypothesis = agent.hypothesis_registry.get(hypothesis_id)
        print(f"✅ Hypothesis generated with {hypothesis.confidence_score:.2f} confidence")
        
        print("\nStep 3: Autonomous experimental design...")
        design_id = agent.design_experiment(hypothesis_id)
        print(f"✅ Experimental design completed")
        
        print("\nStep 4: Multi-step workflow execution simulation...")
        # Create and simulate workflow execution
        workflow_id = workflow_engine.create_compound_discovery_workflow(
            research_question=research_topic,
            target_properties={"longevity_focus": True}
        )
        print(f"✅ Research workflow created: {workflow_id}")
        
        print("\nStep 5: Research insight generation...")
        insight_id = agent.generate_research_insights(
            data_sources=["literature", "knowledge_graph", "experimental"],
            analysis_focus="comprehensive"
        )
        insight = agent.insight_database.get(insight_id)
        print(f"✅ Research insights generated")
        
        # Display investigation summary
        print(f"\n📊 Investigation Summary:")
        print(f"   Topic: {research_topic}")
        print(f"   Hypothesis: {hypothesis.hypothesis_text}")
        print(f"   Confidence: {hypothesis.confidence_score:.2f}")
        print(f"   Priority: {hypothesis.priority.value}")
        print(f"   Insight: {insight.insight_text}")
        print(f"   Insight Confidence: {insight.confidence_level}")
        
        if insight.actionable_recommendations:
            print(f"\n💡 Key Recommendations:")
            for i, rec in enumerate(insight.actionable_recommendations[:3], 1):
                print(f"   {i}. {rec}")
        
        if insight.follow_up_questions:
            print(f"\n❓ Follow-up Questions:")
            for i, question in enumerate(insight.follow_up_questions[:3], 1):
                print(f"   {i}. {question}")
        
        print(f"\n✅ Autonomous investigation completed successfully!")
        
    except Exception as e:
        print(f"❌ Autonomous investigation failed: {e}")

def main():
    """Main demonstration function for Phase 3"""
    print("🤖 LifeMindML-Biomni Integration: Phase 3 Demo")
    print("Autonomous Research Assistant - Multi-Step Workflows & Personalized Intelligence")
    print("=" * 90)
    
    try:
        # Run Phase 3 demonstrations
        demo_autonomous_research_agent()
        demo_personalized_health_intelligence()
        demo_multistep_research_workflows()
        
        # Run autonomous investigation (async demo)
        print("\n🔄 Running autonomous investigation demo...")
        asyncio.run(demo_autonomous_investigation())
        
        print("\n🎉 " + "="*70)
        print("PHASE 3 DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("="*70)
        print("\n✅ Autonomous research agents: WORKING")
        print("✅ Personalized health intelligence: WORKING")
        print("✅ Multi-step research workflows: WORKING")
        print("✅ Autonomous investigation capabilities: WORKING")
        print("✅ Research hypothesis generation: WORKING")
        print("✅ Experimental design automation: WORKING")
        
        print("\n🚀 Ready for Phase 4: Advanced Biomedical AI Platform")
        
    except Exception as e:
        print(f"\n❌ Phase 3 demo failed with error: {e}")
        print("Please check your installation and try again.")

if __name__ == "__main__":
    main()
