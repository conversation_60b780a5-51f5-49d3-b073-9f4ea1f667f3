"""
LifeMindML-Biomni Agent Integration Demo
Demonstrates the intelligent agent workflows for Phase 1 implementation

This script shows how to use the enhanced LifeMindML tools through
intelligent agents for compound discovery, health assessment, and longevity research.
"""

import sys
import os
from typing import Dict, Any

# Add paths for imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

try:
    from biomni.tool.tool_registry import ToolRegistry
    from biomni.agent.lifemind_agents import (
        CompoundDiscoveryAgent,
        HealthAssessmentAgent,
        LongevityResearchAgent
    )
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure LifeMindML and Biomni are properly installed")
    sys.exit(1)

def demo_compound_discovery_agent():
    """Demonstrate the Compound Discovery Agent"""
    print("🧪 " + "="*60)
    print("COMPOUND DISCOVERY AGENT DEMONSTRATION")
    print("="*60)
    
    # Initialize tool registry and agent
    tool_registry = ToolRegistry()
    agent = CompoundDiscoveryAgent(tool_registry)
    
    # Demo 1: Find molecules similar to metformin
    print("\n📋 Demo 1: Find molecules similar to metformin with high lifespan extension probability")
    print("-" * 80)
    
    query = "Find molecules similar to metformin with higher lifespan extension probability"
    report = agent.generate_compound_discovery_report(query)
    print(report)
    
    # Demo 2: Analyze specific compounds
    print("\n📋 Demo 2: Analyze specific anti-aging compounds")
    print("-" * 80)
    
    compounds = agent.find_similar_compounds(
        reference_compound="CN(C)C(=N)N=C(N)N",  # Metformin
        similarity_threshold=0.6
    )
    
    print(f"Found {len([c for c in compounds if c.get('type') == 'candidate'])} candidate compounds:")
    for compound in compounds[:3]:  # Show top 3
        print(f"\n🧬 {compound['compound_name']}")
        print(f"SMILES: {compound['smiles']}")
        if compound.get('probability'):
            print(f"Probability: {compound['probability']:.3f}")

def demo_health_assessment_agent():
    """Demonstrate the Health Assessment Agent"""
    print("\n🏥 " + "="*60)
    print("HEALTH ASSESSMENT AGENT DEMONSTRATION")
    print("="*60)
    
    # Initialize tool registry and agent
    tool_registry = ToolRegistry()
    agent = HealthAssessmentAgent(tool_registry)
    
    # Demo patient data
    patient_data = {
        "patient_id": "DEMO_001",
        "lifestyle": {
            "sleep_hours": 6.5,
            "steps": 5000,
            "calories": 2500,
            "protein": 70,
            "age": 45
        },
        "clinical": {
            "age": 45,
            "ejection_fraction": 55,
            "serum_creatinine": 1.1,
            "serum_sodium": 140,
            "anaemia": 0,
            "diabetes": 0,
            "high_blood_pressure": 1,
            "sex": 1,
            "smoking": 0
        }
    }
    
    print("\n📋 Demo: Comprehensive Health Assessment")
    print("-" * 80)
    
    assessment_report = agent.analyze_patient_profile(patient_data)
    print(assessment_report)
    
    # Generate intervention recommendations
    print("\n💡 Intervention Recommendations")
    print("-" * 80)
    
    assessment_results = {
        "lifestyle_score": 65,
        "clinical_risk": "Moderate",
        "mental_health_concerns": False
    }
    
    recommendations = agent.generate_intervention_recommendations(assessment_results)
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")

def demo_longevity_research_agent():
    """Demonstrate the Longevity Research Agent"""
    print("\n📈 " + "="*60)
    print("LONGEVITY RESEARCH AGENT DEMONSTRATION")
    print("="*60)
    
    # Initialize tool registry and agent
    tool_registry = ToolRegistry()
    agent = LongevityResearchAgent(tool_registry)
    
    # Demo 1: Compare intervention strategies
    print("\n📋 Demo 1: Compare Intervention Strategies")
    print("-" * 80)
    
    baseline_profile = {
        "age": 65,
        "ejection_fraction": 40,
        "serum_creatinine": 1.5,
        "serum_sodium": 135,
        "anaemia": 1,
        "diabetes": 1,
        "high_blood_pressure": 1,
        "sex": 1,
        "smoking": 1
    }
    
    interventions = [
        {
            "name": "Lifestyle Optimization",
            "type": "lifestyle",
            "changes": {
                "smoking": 0,  # Smoking cessation
                "high_blood_pressure": 0  # BP control
            }
        },
        {
            "name": "Clinical Management",
            "type": "clinical",
            "changes": {
                "ejection_fraction": 50,  # Improved EF
                "serum_creatinine": 1.2,  # Better kidney function
                "anaemia": 0  # Treat anaemia
            }
        },
        {
            "name": "Combined Intervention",
            "type": "combined",
            "changes": {
                "smoking": 0,
                "ejection_fraction": 45,
                "serum_creatinine": 1.3,
                "anaemia": 0
            }
        }
    ]
    
    comparison_report = agent.compare_intervention_strategies(baseline_profile, interventions)
    print(comparison_report)
    
    # Demo 2: Generate research hypothesis
    print("\n📋 Demo 2: Generate Research Hypothesis")
    print("-" * 80)
    
    research_questions = [
        "How can we identify novel compounds for healthy aging?",
        "What lifestyle interventions are most effective for longevity?",
        "Can multi-modal health assessment predict longevity outcomes?"
    ]
    
    for question in research_questions:
        print(f"\n🔬 Research Question: {question}")
        hypothesis = agent.generate_research_hypothesis(question)
        print(hypothesis[:500] + "..." if len(hypothesis) > 500 else hypothesis)

def demo_integrated_workflow():
    """Demonstrate integrated workflow using multiple agents"""
    print("\n🤖 " + "="*60)
    print("INTEGRATED MULTI-AGENT WORKFLOW DEMONSTRATION")
    print("="*60)
    
    # Initialize agents
    tool_registry = ToolRegistry()
    compound_agent = CompoundDiscoveryAgent(tool_registry)
    health_agent = HealthAssessmentAgent(tool_registry)
    research_agent = LongevityResearchAgent(tool_registry)
    
    print("\n📋 Scenario: Personalized Longevity Optimization")
    print("-" * 80)
    
    # Step 1: Health Assessment
    print("Step 1: Comprehensive Health Assessment")
    patient_data = {
        "patient_id": "INTEGRATED_DEMO",
        "lifestyle": {
            "sleep_hours": 7.0,
            "steps": 8500,
            "calories": 2200,
            "protein": 85,
            "age": 50
        },
        "clinical": {
            "age": 50,
            "ejection_fraction": 60,
            "serum_creatinine": 1.0,
            "serum_sodium": 142,
            "anaemia": 0,
            "diabetes": 0,
            "high_blood_pressure": 0,
            "sex": 0,
            "smoking": 0
        }
    }
    
    health_report = health_agent.analyze_patient_profile(patient_data)
    print("✅ Health assessment completed")
    
    # Step 2: Compound Discovery for Optimization
    print("\nStep 2: Identify Potential Longevity Compounds")
    compound_query = "Find compounds with high lifespan extension potential for healthy aging"
    compound_report = compound_agent.generate_compound_discovery_report(compound_query)
    print("✅ Compound discovery completed")
    
    # Step 3: Research Strategy Development
    print("\nStep 3: Develop Personalized Research Strategy")
    research_question = "What combination of lifestyle and compound interventions would optimize longevity for this patient profile?"
    research_hypothesis = research_agent.generate_research_hypothesis(research_question)
    print("✅ Research strategy developed")
    
    # Integrated Summary
    print("\n📊 INTEGRATED ANALYSIS SUMMARY")
    print("-" * 80)
    print("✅ Multi-modal health assessment: COMPLETED")
    print("✅ Compound discovery analysis: COMPLETED") 
    print("✅ Research strategy development: COMPLETED")
    print("\n💡 Next Steps:")
    print("1. Implement personalized intervention protocol")
    print("2. Monitor biomarkers and health outcomes")
    print("3. Adjust strategy based on response")
    print("4. Contribute data to longevity research database")

def main():
    """Main demonstration function"""
    print("🧠 LifeMindML-Biomni Integration Demo")
    print("Phase 1: Enhanced Tool Integration - Intelligent Agent Workflows")
    print("=" * 80)
    
    try:
        # Run individual agent demos
        demo_compound_discovery_agent()
        demo_health_assessment_agent()
        demo_longevity_research_agent()
        
        # Run integrated workflow demo
        demo_integrated_workflow()
        
        print("\n🎉 " + "="*60)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("="*60)
        print("\nAll LifeMindML-Biomni intelligent agents are working correctly!")
        print("Ready for Phase 2: Deep Scientific Integration")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        print("Please check your installation and try again.")

if __name__ == "__main__":
    main()
