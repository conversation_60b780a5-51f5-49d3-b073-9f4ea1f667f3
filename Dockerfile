# Multi-stage Docker build for LifeMindML
# Stage 1: Base image with system dependencies
FROM python:3.9-slim as base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    software-properties-common \
    git \
    libsndfile1 \
    ffmpeg \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd --create-home --shell /bin/bash lifemind
WORKDIR /home/<USER>

# Stage 2: Dependencies installation
FROM base as dependencies

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-docker.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-docker.txt

# Stage 3: Application
FROM dependencies as application

# Copy application code
COPY --chown=lifemind:lifemind . .

# Create necessary directories
RUN mkdir -p logs models data && \
    chown -R lifemind:lifemind /home/<USER>

# Switch to non-root user
USER lifemind

# Expose ports
EXPOSE 8000 8501

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command (can be overridden)
CMD ["python", "-m", "uvicorn", "src.unify:app", "--host", "0.0.0.0", "--port", "8000"]

# Stage 4: Development image with additional tools
FROM application as development

USER root

# Install development dependencies
RUN pip install --no-cache-dir \
    jupyter \
    pytest \
    black \
    flake8 \
    mypy \
    pre-commit

# Install additional development tools
RUN apt-get update && apt-get install -y \
    vim \
    htop \
    tree \
    && rm -rf /var/lib/apt/lists/*

USER lifemind

# Development command
CMD ["python", "-m", "uvicorn", "src.unify:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Stage 5: Production image (optimized)
FROM application as production

# Remove unnecessary files
RUN rm -rf \
    notebooks \
    research \
    .git \
    .gitignore \
    README.md \
    *.md

# Production optimizations
ENV PYTHONOPTIMIZE=1

# Production command with gunicorn
CMD ["gunicorn", "src.unify:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
