version: '3.8'

services:
  # Development API service with hot reload
  lifemindml-api-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: lifemindml-api-dev
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/home/<USER>
      - LOG_LEVEL=DEBUG
      - MODEL_PATH=/home/<USER>/models
      - DEVELOPMENT=true
    volumes:
      - .:/home/<USER>
      - ./logs:/home/<USER>/logs
    restart: unless-stopped
    networks:
      - lifemindml-dev-network
    depends_on:
      - redis-dev

  # Development Streamlit service
  lifemindml-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: lifemindml-frontend-dev
    ports:
      - "8501:8501"
    environment:
      - PYTHONPATH=/home/<USER>
      - API_BASE_URL=http://lifemindml-api-dev:8000
      - DEVELOPMENT=true
    volumes:
      - .:/home/<USER>
    command: ["streamlit", "run", "app/streamlit_app.py", "--server.address", "0.0.0.0", "--server.port", "8501", "--server.runOnSave", "true"]
    restart: unless-stopped
    networks:
      - lifemindml-dev-network
    depends_on:
      - lifemindml-api-dev

  # Jupyter notebook service for development
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: lifemindml-jupyter
    ports:
      - "8888:8888"
    environment:
      - PYTHONPATH=/home/<USER>
    volumes:
      - .:/home/<USER>
    command: ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root", "--NotebookApp.token=''", "--NotebookApp.password=''"]
    restart: unless-stopped
    networks:
      - lifemindml-dev-network

  # Redis for development
  redis-dev:
    image: redis:7-alpine
    container_name: lifemindml-redis-dev
    ports:
      - "6379:6379"
    networks:
      - lifemindml-dev-network

  # PostgreSQL for development (if needed)
  postgres-dev:
    image: postgres:15-alpine
    container_name: lifemindml-postgres-dev
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=lifemindml
      - POSTGRES_USER=lifemind
      - POSTGRES_PASSWORD=lifemind123
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
    networks:
      - lifemindml-dev-network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: lifemindml-pgadmin
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=lifemind123
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    networks:
      - lifemindml-dev-network
    depends_on:
      - postgres-dev

volumes:
  postgres-dev-data:
  pgadmin-data:

networks:
  lifemindml-dev-network:
    driver: bridge
