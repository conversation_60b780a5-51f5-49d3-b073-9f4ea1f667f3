"""
Test Suite for LifeMindML-Biomni Integration
Phase 1: Enhanced Tool Integration Testing

This module contains comprehensive tests for the Biomni-LifeMindML integration,
including tool registration, agent functionality, and workflow validation.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import json

# Add paths for imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../Biomni')))

try:
    from Biomni.biomni.tool.tool_registry import ToolRegistry
    from Biomni.biomni.tool.lifemind_drug import LIFEMIND_TOOLS
    from Biomni.biomni.agent.lifemind_agents import (
        CompoundDiscoveryAgent,
        HealthAssessmentAgent,
        LongevityResearchAgent
    )
except ImportError as e:
    print(f"Warning: Could not import modules for testing: {e}")

class TestToolRegistration(unittest.TestCase):
    """Test tool registration and validation"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tool_registry = ToolRegistry()
    
    def test_tool_registry_initialization(self):
        """Test that tool registry initializes correctly"""
        self.assertIsInstance(self.tool_registry, ToolRegistry)
        self.assertGreater(len(self.tool_registry.tools), 0)
    
    def test_lifemind_tools_registered(self):
        """Test that all LifeMindML tools are registered"""
        expected_tools = [
            "lifespan_drug_predictor",
            "disease_detector", 
            "lifestyle_optimizer",
            "survival_analyzer",
            "voice_emotion_analyzer",
            "comprehensive_health_assessor"
        ]
        
        registered_tools = [tool["name"] for tool in self.tool_registry.tools]
        
        for expected_tool in expected_tools:
            self.assertIn(expected_tool, registered_tools, 
                         f"Tool {expected_tool} not found in registry")
    
    def test_tool_validation(self):
        """Test tool validation functionality"""
        # Valid tool
        valid_tool = {
            "name": "test_tool",
            "description": "Test tool description",
            "required_parameters": {"param1": "Test parameter"}
        }
        self.assertTrue(self.tool_registry.validate_tool(valid_tool))
        
        # Invalid tool (missing required keys)
        invalid_tool = {
            "name": "invalid_tool",
            "description": "Missing required_parameters"
        }
        self.assertFalse(self.tool_registry.validate_tool(invalid_tool))
    
    def test_tool_retrieval(self):
        """Test tool retrieval by name and ID"""
        # Test retrieval by name
        drug_tool = self.tool_registry.get_tool_by_name("lifespan_drug_predictor")
        self.assertIsNotNone(drug_tool)
        self.assertEqual(drug_tool["name"], "lifespan_drug_predictor")
        
        # Test retrieval by ID
        tool_id = drug_tool["id"]
        retrieved_tool = self.tool_registry.get_tool_by_id(tool_id)
        self.assertEqual(retrieved_tool, drug_tool)

class TestCompoundDiscoveryAgent(unittest.TestCase):
    """Test Compound Discovery Agent functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tool_registry = ToolRegistry()
        self.agent = CompoundDiscoveryAgent(self.tool_registry)
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        self.assertIsInstance(self.agent, CompoundDiscoveryAgent)
        self.assertIsNotNone(self.agent.drug_tool)
        self.assertIsInstance(self.agent.compound_database, dict)
    
    def test_compound_database_loading(self):
        """Test compound database loading"""
        expected_compounds = ["Metformin", "Aspirin", "Resveratrol"]
        
        for compound in expected_compounds:
            self.assertIn(compound, self.agent.compound_database)
            self.assertIsInstance(self.agent.compound_database[compound], str)
    
    @patch('Biomni.biomni.tool.lifemind_drug.predict_lifespan_effect')
    def test_find_similar_compounds(self, mock_predict):
        """Test finding similar compounds"""
        # Mock prediction function
        mock_predict.return_value = 0.75
        
        reference_smiles = "CN(C)C(=N)N=C(N)N"  # Metformin
        results = self.agent.find_similar_compounds(reference_smiles, 0.5)
        
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
        
        # Check that reference compound is included
        reference_found = any(r.get("type") == "reference" for r in results)
        self.assertTrue(reference_found)
    
    def test_generate_compound_discovery_report(self):
        """Test compound discovery report generation"""
        query = "Find molecules similar to metformin"
        
        with patch.object(self.agent, 'find_similar_compounds') as mock_find:
            mock_find.return_value = [
                {
                    "compound_name": "Test Compound",
                    "smiles": "CC(=O)O",
                    "analysis": "Test analysis",
                    "probability": 0.8,
                    "type": "candidate"
                }
            ]
            
            report = self.agent.generate_compound_discovery_report(query)
            
            self.assertIsInstance(report, str)
            self.assertIn("Compound Discovery Report", report)
            self.assertIn(query, report)

class TestHealthAssessmentAgent(unittest.TestCase):
    """Test Health Assessment Agent functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tool_registry = ToolRegistry()
        self.agent = HealthAssessmentAgent(self.tool_registry)
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        self.assertIsInstance(self.agent, HealthAssessmentAgent)
        self.assertIsNotNone(self.agent.lifestyle_tool)
        self.assertIsNotNone(self.agent.survival_tool)
    
    def test_analyze_patient_profile(self):
        """Test patient profile analysis"""
        patient_data = {
            "patient_id": "TEST_001",
            "lifestyle": {
                "sleep_hours": 7.5,
                "steps": 8000,
                "calories": 2200,
                "protein": 80
            },
            "clinical": {
                "age": 45,
                "ejection_fraction": 55,
                "serum_creatinine": 1.1,
                "serum_sodium": 140
            }
        }
        
        with patch.object(self.agent.lifestyle_tool, '__getitem__') as mock_lifestyle:
            with patch.object(self.agent.survival_tool, '__getitem__') as mock_survival:
                mock_lifestyle.return_value = lambda **kwargs: "Lifestyle analysis result"
                mock_survival.return_value = lambda **kwargs: "Clinical analysis result"
                
                report = self.agent.analyze_patient_profile(patient_data)
                
                self.assertIsInstance(report, str)
                self.assertIn("Comprehensive Health Assessment", report)
                self.assertIn("TEST_001", report)
    
    def test_generate_intervention_recommendations(self):
        """Test intervention recommendation generation"""
        assessment_results = {
            "lifestyle_score": 65,
            "clinical_risk": "High",
            "mental_health_concerns": True
        }
        
        recommendations = self.agent.generate_intervention_recommendations(assessment_results)
        
        self.assertIsInstance(recommendations, list)
        self.assertGreater(len(recommendations), 0)
        
        # Check for specific recommendations based on input
        recommendation_text = " ".join(recommendations)
        self.assertIn("cardiology", recommendation_text.lower())

class TestLongevityResearchAgent(unittest.TestCase):
    """Test Longevity Research Agent functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tool_registry = ToolRegistry()
        self.agent = LongevityResearchAgent(self.tool_registry)
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        self.assertIsInstance(self.agent, LongevityResearchAgent)
        self.assertIsNotNone(self.agent.survival_tool)
        self.assertIsNotNone(self.agent.drug_tool)
    
    def test_compare_intervention_strategies(self):
        """Test intervention strategy comparison"""
        baseline_profile = {
            "age": 65,
            "ejection_fraction": 40,
            "serum_creatinine": 1.5,
            "serum_sodium": 135
        }
        
        interventions = [
            {
                "name": "Test Intervention",
                "type": "lifestyle",
                "changes": {"smoking": 0}
            }
        ]
        
        with patch.object(self.agent.survival_tool, '__getitem__') as mock_survival:
            mock_survival.return_value = lambda **kwargs: "365-Day Survival Probability: 75.0%"
            
            report = self.agent.compare_intervention_strategies(baseline_profile, interventions)
            
            self.assertIsInstance(report, str)
            self.assertIn("Longevity Intervention Comparison", report)
            self.assertIn("Test Intervention", report)
    
    def test_generate_research_hypothesis(self):
        """Test research hypothesis generation"""
        research_questions = [
            "How can we identify novel compounds for healthy aging?",
            "What lifestyle interventions are most effective?",
            "Can multi-modal assessment predict longevity?"
        ]
        
        for question in research_questions:
            hypothesis = self.agent.generate_research_hypothesis(question)
            
            self.assertIsInstance(hypothesis, str)
            self.assertIn("Research Hypothesis Generation", hypothesis)
            self.assertIn(question, hypothesis)

class TestIntegrationWorkflow(unittest.TestCase):
    """Test integrated workflow functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tool_registry = ToolRegistry()
        self.compound_agent = CompoundDiscoveryAgent(self.tool_registry)
        self.health_agent = HealthAssessmentAgent(self.tool_registry)
        self.research_agent = LongevityResearchAgent(self.tool_registry)
    
    def test_multi_agent_workflow(self):
        """Test multi-agent workflow integration"""
        # Test that agents can work together
        self.assertIsInstance(self.compound_agent, CompoundDiscoveryAgent)
        self.assertIsInstance(self.health_agent, HealthAssessmentAgent)
        self.assertIsInstance(self.research_agent, LongevityResearchAgent)
        
        # Test that they share the same tool registry
        self.assertEqual(self.compound_agent.tool_registry, self.tool_registry)
        self.assertEqual(self.health_agent.tool_registry, self.tool_registry)
        self.assertEqual(self.research_agent.tool_registry, self.tool_registry)
    
    def test_configuration_loading(self):
        """Test configuration file loading"""
        config_path = "Biomni/config/lifemind_integration.json"
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            self.assertIn("lifemind_biomni_integration", config)
            self.assertIn("tools", config["lifemind_biomni_integration"])
            self.assertIn("agents", config["lifemind_biomni_integration"])

class TestErrorHandling(unittest.TestCase):
    """Test error handling and edge cases"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tool_registry = ToolRegistry()
    
    def test_invalid_tool_registration(self):
        """Test handling of invalid tool registration"""
        invalid_tool = {"name": "invalid"}  # Missing required fields
        
        with self.assertRaises(ValueError):
            self.tool_registry.register_tool(invalid_tool)
    
    def test_nonexistent_tool_retrieval(self):
        """Test retrieval of non-existent tools"""
        result = self.tool_registry.get_tool_by_name("nonexistent_tool")
        self.assertIsNone(result)
        
        result = self.tool_registry.get_tool_by_id(99999)
        self.assertIsNone(result)
    
    def test_agent_with_missing_tools(self):
        """Test agent behavior when tools are missing"""
        # Create empty registry
        empty_registry = ToolRegistry.__new__(ToolRegistry)
        empty_registry.tools = []
        empty_registry.next_id = 0
        
        # Agent should handle missing tools gracefully
        agent = CompoundDiscoveryAgent(empty_registry)
        self.assertIsNone(agent.drug_tool)

def run_integration_tests():
    """Run all integration tests"""
    print("🧪 Running LifeMindML-Biomni Integration Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestToolRegistration,
        TestCompoundDiscoveryAgent,
        TestHealthAssessmentAgent,
        TestLongevityResearchAgent,
        TestIntegrationWorkflow,
        TestErrorHandling
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All tests passed! Integration is working correctly.")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
