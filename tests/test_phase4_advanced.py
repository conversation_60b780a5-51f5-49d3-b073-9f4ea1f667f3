"""
Test Suite for Phase 4: Advanced Biomedical AI Platform
Tests multi-modal reasoning, research acceleration, and advanced AI platform integration

This module contains comprehensive tests for Phase 4 implementation,
including multi-modal biomedical reasoning, research acceleration platform, and AI platform integration.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import json
from datetime import datetime
from typing import Dict, Any, List

# Add paths for imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../Biomni')))

try:
    from Biomni.biomni.platform.multimodal_reasoning import (
        MultiModalBiomedicalReasoner, ModalityType, ReasoningType, ModalityData, 
        ReasoningResult, CrossModalAlignment
    )
    from Biomni.biomni.platform.research_acceleration import (
        ResearchAccelerationPlatform, ResearchProject, AccelerationRecommendation, 
        AccelerationType, ResearchPhase
    )
    from Biomni.biomni.platform.ai_platform import (
        AdvancedBiomedicalAIPlatform, PlatformSession, ServiceType, IntegrationLevel, PlatformMode
    )
except ImportError as e:
    print(f"Warning: Could not import Phase 4 modules for testing: {e}")

class TestMultiModalReasoning(unittest.TestCase):
    """Test multi-modal biomedical reasoning system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.reasoner = MultiModalBiomedicalReasoner()
    
    def test_reasoner_initialization(self):
        """Test multi-modal reasoner initialization"""
        self.assertIsInstance(self.reasoner, MultiModalBiomedicalReasoner)
        self.assertIsInstance(self.reasoner.modality_processors, dict)
        self.assertIsInstance(self.reasoner.reasoning_engines, dict)
        self.assertIsInstance(self.reasoner.integration_strategies, dict)
        self.assertIsInstance(self.reasoner.knowledge_base, dict)
        
        # Check all modality types are supported
        for modality_type in ModalityType:
            self.assertIn(modality_type, self.reasoner.modality_processors)
        
        # Check all reasoning types are supported
        for reasoning_type in ReasoningType:
            self.assertIn(reasoning_type, self.reasoner.reasoning_engines)
    
    def test_modality_data_processing(self):
        """Test processing of different modality data"""
        # Create test modality data
        test_modalities = [
            ModalityData(
                modality_type=ModalityType.TEXT,
                data="Metformin is an anti-diabetic drug with potential anti-aging effects",
                metadata={"source": "literature"},
                quality_score=0.9,
                confidence=0.8,
                source="test",
                timestamp=datetime.now()
            ),
            ModalityData(
                modality_type=ModalityType.MOLECULAR,
                data={"smiles": "CN(C)C(=N)N=C(N)N", "compound": "Metformin"},
                metadata={"database": "ChEMBL"},
                quality_score=0.95,
                confidence=0.9,
                source="test",
                timestamp=datetime.now()
            ),
            ModalityData(
                modality_type=ModalityType.CLINICAL,
                data={"glucose": 95, "hba1c": 5.2, "age": 45},
                metadata={"study": "diabetes_trial"},
                quality_score=0.85,
                confidence=0.8,
                source="test",
                timestamp=datetime.now()
            )
        ]
        
        # Process modality data
        processed_data = self.reasoner.process_multimodal_input(test_modalities)
        
        self.assertIsInstance(processed_data, dict)
        self.assertIn("text", processed_data)
        self.assertIn("molecular", processed_data)
        self.assertIn("clinical", processed_data)
        
        # Check processing results
        for modality_key, processed in processed_data.items():
            self.assertIsInstance(processed, dict)
            if "error" not in processed:
                self.assertGreater(len(processed), 0)
    
    def test_cross_modal_alignment(self):
        """Test cross-modal data alignment"""
        # Create test modality data
        test_modalities = [
            ModalityData(
                modality_type=ModalityType.MOLECULAR,
                data={"compound": "Rapamycin"},
                metadata={},
                quality_score=0.9,
                confidence=0.8,
                source="test",
                timestamp=datetime.now()
            ),
            ModalityData(
                modality_type=ModalityType.CLINICAL,
                data={"biomarkers": {"mtor_activity": 0.7}},
                metadata={},
                quality_score=0.85,
                confidence=0.8,
                source="test",
                timestamp=datetime.now()
            )
        ]
        
        # Test different alignment strategies
        alignment_strategies = ["early_fusion", "late_fusion", "attention_fusion", "hierarchical_fusion", "graph_fusion"]
        
        for strategy in alignment_strategies:
            alignment = self.reasoner.align_cross_modal_data(test_modalities, strategy)
            
            self.assertIsInstance(alignment, CrossModalAlignment)
            self.assertEqual(len(alignment.modalities), 2)
            self.assertEqual(alignment.integration_method, strategy)
            self.assertIsInstance(alignment.alignment_score, float)
            self.assertGreaterEqual(alignment.alignment_score, 0.0)
            self.assertLessEqual(alignment.alignment_score, 1.0)
            self.assertIsInstance(alignment.aligned_features, dict)
            self.assertIsInstance(alignment.consistency_metrics, dict)
    
    def test_multimodal_reasoning(self):
        """Test multi-modal reasoning execution"""
        # Create test modality data
        test_modalities = [
            ModalityData(
                modality_type=ModalityType.MOLECULAR,
                data={"compound": "Metformin", "pathway": "AMPK"},
                metadata={},
                quality_score=0.9,
                confidence=0.8,
                source="test",
                timestamp=datetime.now()
            ),
            ModalityData(
                modality_type=ModalityType.CLINICAL,
                data={"glucose": 90, "age": 50},
                metadata={},
                quality_score=0.85,
                confidence=0.8,
                source="test",
                timestamp=datetime.now()
            )
        ]
        
        # Test different reasoning types
        for reasoning_type in ReasoningType:
            reasoning_result = self.reasoner.perform_multimodal_reasoning(
                modality_data_list=test_modalities,
                reasoning_type=reasoning_type,
                research_question="Test research question"
            )
            
            self.assertIsInstance(reasoning_result, ReasoningResult)
            self.assertEqual(reasoning_result.reasoning_type, reasoning_type)
            self.assertEqual(len(reasoning_result.input_modalities), 2)
            self.assertIsInstance(reasoning_result.conclusion, str)
            self.assertGreater(len(reasoning_result.conclusion), 0)
            self.assertIsInstance(reasoning_result.confidence_score, float)
            self.assertGreaterEqual(reasoning_result.confidence_score, 0.0)
            self.assertLessEqual(reasoning_result.confidence_score, 1.0)
            self.assertIsInstance(reasoning_result.evidence_chain, list)
            self.assertIsInstance(reasoning_result.supporting_data, dict)
            self.assertIsInstance(reasoning_result.alternative_hypotheses, list)
            self.assertIsInstance(reasoning_result.uncertainty_factors, list)
            self.assertIsInstance(reasoning_result.clinical_relevance, float)

class TestResearchAcceleration(unittest.TestCase):
    """Test research acceleration platform"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.platform = ResearchAccelerationPlatform()
    
    def test_platform_initialization(self):
        """Test research acceleration platform initialization"""
        self.assertIsInstance(self.platform, ResearchAccelerationPlatform)
        self.assertIsInstance(self.platform.research_projects, dict)
        self.assertIsInstance(self.platform.acceleration_recommendations, dict)
        self.assertIsInstance(self.platform.collaboration_opportunities, list)
        self.assertIsInstance(self.platform.acceleration_engines, dict)
        
        # Check acceleration engines are available
        for accel_type in AccelerationType:
            self.assertIn(accel_type, self.platform.acceleration_engines)
            self.assertTrue(callable(self.platform.acceleration_engines[accel_type]))
    
    def test_research_project_creation(self):
        """Test research project creation"""
        project_data = {
            "title": "Test Research Project",
            "description": "A test project for validation",
            "research_domain": "longevity",
            "principal_investigator": "Dr. Test",
            "collaborators": ["Dr. A", "Dr. B"],
            "research_questions": ["Question 1", "Question 2"],
            "hypotheses": ["Hypothesis 1"],
            "methodologies": ["Method 1", "Method 2"],
            "budget": 100000.0,
            "expected_duration_months": 12,
            "required_expertise": ["expertise_1", "expertise_2"],
            "equipment_needs": ["equipment_1"]
        }
        
        project_id = self.platform.create_research_project(project_data)
        
        self.assertIsInstance(project_id, str)
        self.assertIn(project_id, self.platform.research_projects)
        
        project = self.platform.research_projects[project_id]
        self.assertIsInstance(project, ResearchProject)
        self.assertEqual(project.title, project_data["title"])
        self.assertEqual(project.research_domain, project_data["research_domain"])
        self.assertEqual(project.budget, project_data["budget"])
        self.assertEqual(project.current_phase, ResearchPhase.IDEATION)
        self.assertIsInstance(project.acceleration_potential, dict)
        self.assertGreater(len(project.acceleration_potential), 0)
    
    def test_acceleration_opportunity_analysis(self):
        """Test acceleration opportunity analysis"""
        # Create a test project
        project_data = {
            "title": "Acceleration Test Project",
            "description": "Testing acceleration opportunities",
            "research_domain": "drug_discovery",
            "budget": 150000.0,
            "expected_duration_months": 18,
            "research_questions": ["How can we accelerate drug discovery?"],
            "hypotheses": ["AI can accelerate screening"],
            "methodologies": ["computational_screening", "experimental_validation"]
        }
        
        project_id = self.platform.create_research_project(project_data)
        
        # Analyze acceleration opportunities
        recommendation_ids = self.platform.analyze_acceleration_opportunities(project_id)
        
        self.assertIsInstance(recommendation_ids, list)
        self.assertIn(project_id, self.platform.acceleration_recommendations)
        
        recommendations = self.platform.acceleration_recommendations[project_id]
        self.assertEqual(len(recommendations), len(recommendation_ids))
        
        # Check recommendation properties
        for recommendation in recommendations:
            self.assertIsInstance(recommendation, AccelerationRecommendation)
            self.assertEqual(recommendation.project_id, project_id)
            self.assertIsInstance(recommendation.acceleration_type, AccelerationType)
            self.assertIsInstance(recommendation.time_savings_months, float)
            self.assertIsInstance(recommendation.cost_savings, float)
            self.assertIsInstance(recommendation.quality_improvement, float)
            self.assertIsInstance(recommendation.success_probability, float)
            self.assertGreaterEqual(recommendation.success_probability, 0.0)
            self.assertLessEqual(recommendation.success_probability, 1.0)
    
    def test_collaboration_opportunity_identification(self):
        """Test collaboration opportunity identification"""
        # Create multiple test projects
        project_data_1 = {
            "title": "Project A",
            "research_domain": "longevity",
            "required_expertise": ["computational_biology", "aging_research"],
            "budget": 100000.0
        }
        
        project_data_2 = {
            "title": "Project B", 
            "research_domain": "longevity",
            "required_expertise": ["pharmacology", "aging_research"],
            "budget": 120000.0
        }
        
        project_id_1 = self.platform.create_research_project(project_data_1)
        project_id_2 = self.platform.create_research_project(project_data_2)
        
        # Identify collaboration opportunities
        collaboration_ids = self.platform.identify_collaboration_opportunities()
        
        self.assertIsInstance(collaboration_ids, list)
        
        # Check if collaborations were identified
        if collaboration_ids:
            self.assertGreater(len(self.platform.collaboration_opportunities), 0)
            
            for opportunity in self.platform.collaboration_opportunities:
                self.assertIsInstance(opportunity.synergy_score, float)
                self.assertGreaterEqual(opportunity.synergy_score, 0.0)
                self.assertLessEqual(opportunity.synergy_score, 1.0)
                self.assertIsInstance(opportunity.complementary_expertise, list)
                self.assertIsInstance(opportunity.expected_benefits, list)

class TestAdvancedAIPlatform(unittest.TestCase):
    """Test advanced AI platform integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.platform = AdvancedBiomedicalAIPlatform()
    
    def test_platform_initialization(self):
        """Test AI platform initialization"""
        self.assertIsInstance(self.platform, AdvancedBiomedicalAIPlatform)
        self.assertIsInstance(self.platform.platform_id, str)
        self.assertIsInstance(self.platform.active_sessions, dict)
        self.assertIsInstance(self.platform.service_registry, dict)
        self.assertIsInstance(self.platform.integration_layers, dict)
        self.assertIsInstance(self.platform.analytics_engine, dict)
        
        # Check service registry
        for service_type in ServiceType:
            self.assertIn(service_type, self.platform.service_registry)
            service_info = self.platform.service_registry[service_type]
            self.assertIn("engines", service_info)
            self.assertIn("tools", service_info)
            self.assertIn("capabilities", service_info)
            self.assertIn("integration_level", service_info)
        
        # Check integration layers
        for integration_level in IntegrationLevel:
            self.assertIn(integration_level, self.platform.integration_layers)
            layer_info = self.platform.integration_layers[integration_level]
            self.assertIn("automation_level", layer_info)
            self.assertIn("services", layer_info)
            self.assertIn("capabilities", layer_info)
    
    def test_platform_session_creation(self):
        """Test platform session creation"""
        user_id = "test_user_001"
        session_type = "research"
        preferences = {"analysis_depth": "comprehensive"}
        
        session_id = self.platform.create_platform_session(
            user_id=user_id,
            session_type=session_type,
            preferences=preferences
        )
        
        self.assertIsInstance(session_id, str)
        self.assertIn(session_id, self.platform.active_sessions)
        
        session = self.platform.active_sessions[session_id]
        self.assertIsInstance(session, PlatformSession)
        self.assertEqual(session.user_id, user_id)
        self.assertEqual(session.session_type, session_type)
        self.assertEqual(session.preferences, preferences)
        self.assertIsInstance(session.start_time, datetime)
        self.assertIsInstance(session.last_activity, datetime)
    
    def test_integrated_analysis_execution(self):
        """Test integrated analysis execution"""
        # Create session
        session_id = self.platform.create_platform_session("test_user", "research")
        
        # Create analysis request
        analysis_request = {
            "analysis_type": "comprehensive",
            "input_data": {
                "compounds": [{"name": "Metformin", "smiles": "CN(C)C(=N)N=C(N)N"}],
                "lifestyle_data": {"sleep_hours": 7.5, "steps": 8000},
                "clinical_data": {"age": 45, "glucose": 95}
            },
            "services": ["prediction", "analysis"]
        }
        
        # Execute analysis
        results = self.platform.execute_integrated_analysis(session_id, analysis_request)
        
        self.assertIsInstance(results, dict)
        
        if "error" not in results:
            self.assertIn("request_id", results)
            self.assertIn("session_id", results)
            self.assertIn("analysis_type", results)
            self.assertIn("services_executed", results)
            self.assertIn("results", results)
            self.assertIn("integration_level", results)
            self.assertIn("platform_mode", results)
            
            self.assertEqual(results["session_id"], session_id)
            self.assertEqual(results["analysis_type"], "comprehensive")
            self.assertIsInstance(results["services_executed"], list)
            self.assertIsInstance(results["results"], dict)
    
    def test_platform_status_retrieval(self):
        """Test platform status retrieval"""
        status = self.platform.get_platform_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn("platform_id", status)
        self.assertIn("initialization_time", status)
        self.assertIn("current_mode", status)
        self.assertIn("integration_level", status)
        self.assertIn("active_sessions", status)
        self.assertIn("service_registry", status)
        self.assertIn("integration_layers", status)
        self.assertIn("health_status", status)
        
        self.assertEqual(status["platform_id"], self.platform.platform_id)
        self.assertIsInstance(status["active_sessions"], int)
        self.assertEqual(status["health_status"], "operational")
        
        # Check service registry in status
        service_registry = status["service_registry"]
        self.assertIsInstance(service_registry, dict)
        for service_name, service_info in service_registry.items():
            self.assertIn("engines", service_info)
            self.assertIn("tools", service_info)
            self.assertIn("capabilities", service_info)
            self.assertIn("integration_level", service_info)

def run_phase4_tests():
    """Run all Phase 4 tests"""
    print("🤖 Running Phase 4: Advanced Biomedical AI Platform Tests")
    print("=" * 70)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestMultiModalReasoning,
        TestResearchAcceleration,
        TestAdvancedAIPlatform
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("✅ All Phase 4 tests passed! Advanced biomedical AI platform is working correctly.")
        print("\n🚀 Phase 4 Implementation Status:")
        print("✅ Multi-modal biomedical reasoning: TESTED")
        print("✅ Research acceleration platform: TESTED")
        print("✅ Advanced AI platform integration: TESTED")
        print("✅ Cross-modal data alignment: TESTED")
        print("✅ Service orchestration: TESTED")
        print("✅ Platform session management: TESTED")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_phase4_tests()
    sys.exit(0 if success else 1)
