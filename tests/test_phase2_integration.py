"""
Test Suite for Phase 2: Deep Scientific Integration
Tests literature integration, knowledge graph, and enhanced predictions

This module contains comprehensive tests for Phase 2 implementation,
including literature analysis, pathway integration, and enhanced tools.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import json
import numpy as np

# Add paths for imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../Biomni')))

try:
    from Biomni.biomni.integration.literature_engine import (
        LiteratureAnalyzer, PubMedSearchEngine, LiteratureEvidence, EnhancedPrediction
    )
    from Biomni.biomni.integration.knowledge_graph import (
        BiomedicalKnowledgeGraph, KnowledgeNode, KnowledgeEdge, PathwayAnalysis
    )
    from Biomni.biomni.integration.enhanced_prediction import (
        EnhancedPredictionEngine, ComprehensiveAnalysis
    )
    from Biomni.biomni.tool.tool_registry import ToolRegistry
except ImportError as e:
    print(f"Warning: Could not import Phase 2 modules for testing: {e}")

class TestLiteratureEngine(unittest.TestCase):
    """Test literature analysis engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.literature_analyzer = LiteratureAnalyzer()
        self.pubmed_engine = PubMedSearchEngine()
    
    def test_pubmed_engine_initialization(self):
        """Test PubMed search engine initialization"""
        self.assertIsInstance(self.pubmed_engine, PubMedSearchEngine)
        self.assertEqual(self.pubmed_engine.rate_limit_delay, 0.34)
        self.assertIsNotNone(self.pubmed_engine.base_url)
    
    @patch('requests.get')
    def test_pubmed_search(self, mock_get):
        """Test PubMed search functionality"""
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.content = b'<eSearchResult><IdList><Id>12345</Id><Id>67890</Id></IdList></eSearchResult>'
        mock_get.return_value = mock_response
        
        pmids = self.pubmed_engine.search_pubmed("metformin longevity", max_results=5)
        
        self.assertIsInstance(pmids, list)
        self.assertEqual(len(pmids), 2)
        self.assertIn("12345", pmids)
        self.assertIn("67890", pmids)
    
    @patch('requests.get')
    def test_article_fetch(self, mock_get):
        """Test article details fetching"""
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.content = b'''
        <PubmedArticleSet>
            <PubmedArticle>
                <MedlineCitation>
                    <PMID>12345</PMID>
                    <Article>
                        <ArticleTitle>Test Article</ArticleTitle>
                        <Abstract><AbstractText>Test abstract</AbstractText></Abstract>
                        <AuthorList>
                            <Author><LastName>Smith</LastName><ForeName>John</ForeName></Author>
                        </AuthorList>
                    </Article>
                    <MedlineJournalInfo><MedlineTA>Test Journal</MedlineTA></MedlineJournalInfo>
                </MedlineCitation>
                <PubmedData>
                    <History>
                        <PubMedPubDate PubStatus="pubmed">
                            <Year>2023</Year>
                        </PubMedPubDate>
                    </History>
                </PubmedData>
            </PubmedArticle>
        </PubmedArticleSet>
        '''
        mock_get.return_value = mock_response
        
        articles = self.pubmed_engine.fetch_article_details(["12345"])
        
        self.assertIsInstance(articles, list)
        self.assertEqual(len(articles), 1)
        self.assertEqual(articles[0]['pmid'], "12345")
        self.assertEqual(articles[0]['title'], "Test Article")
    
    def test_evidence_classification(self):
        """Test evidence type classification"""
        # Test supporting evidence
        supporting_text = "significant improvement in lifespan increased longevity"
        evidence_type = self.literature_analyzer._classify_evidence_type(supporting_text)
        self.assertEqual(evidence_type, "supporting")
        
        # Test contradicting evidence
        contradicting_text = "no effect on lifespan decreased survival harmful"
        evidence_type = self.literature_analyzer._classify_evidence_type(contradicting_text)
        self.assertEqual(evidence_type, "contradicting")
        
        # Test neutral evidence
        neutral_text = "unclear results mixed findings inconclusive"
        evidence_type = self.literature_analyzer._classify_evidence_type(neutral_text)
        self.assertEqual(evidence_type, "neutral")
    
    def test_relevance_scoring(self):
        """Test relevance score calculation"""
        # High relevance text
        high_relevance = "longevity lifespan aging healthspan mortality exercise diet"
        score = self.literature_analyzer._calculate_relevance_score(high_relevance, "longevity")
        self.assertGreater(score, 0.5)
        
        # Low relevance text
        low_relevance = "unrelated topic completely different subject"
        score = self.literature_analyzer._calculate_relevance_score(low_relevance, "longevity")
        self.assertLess(score, 0.3)
    
    def test_evidence_confidence_calculation(self):
        """Test evidence confidence calculation"""
        # High-quality article
        high_quality_article = {
            'journal': 'Nature Medicine',
            'authors': ['Author1', 'Author2']
        }
        high_quality_text = "randomized controlled trial meta-analysis n=1000 participants"
        confidence = self.literature_analyzer._calculate_evidence_confidence(high_quality_text, high_quality_article)
        self.assertGreater(confidence, 0.7)
        
        # Lower-quality article
        low_quality_article = {
            'journal': 'Unknown Journal',
            'authors': ['Author1']
        }
        low_quality_text = "case report small study"
        confidence = self.literature_analyzer._calculate_evidence_confidence(low_quality_text, low_quality_article)
        self.assertLess(confidence, 0.8)

class TestKnowledgeGraph(unittest.TestCase):
    """Test biomedical knowledge graph"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.knowledge_graph = BiomedicalKnowledgeGraph()
    
    def test_knowledge_graph_initialization(self):
        """Test knowledge graph initialization"""
        self.assertIsInstance(self.knowledge_graph, BiomedicalKnowledgeGraph)
        self.assertIsNotNone(self.knowledge_graph.pathway_database)
        self.assertIsNotNone(self.knowledge_graph.disease_network)
        self.assertIsNotNone(self.knowledge_graph.lifestyle_interactions)
    
    def test_pathway_database_structure(self):
        """Test pathway database structure"""
        pathways = self.knowledge_graph.pathway_database
        
        # Check aging pathways
        self.assertIn("aging_pathways", pathways)
        aging_pathways = pathways["aging_pathways"]
        
        # Check specific pathways
        expected_pathways = ["mtor_pathway", "sirtuin_pathway", "autophagy_pathway"]
        for pathway in expected_pathways:
            self.assertIn(pathway, aging_pathways)
            
            # Check pathway structure
            pathway_data = aging_pathways[pathway]
            self.assertIn("name", pathway_data)
            self.assertIn("description", pathway_data)
            self.assertIn("genes", pathway_data)
            self.assertIn("compounds", pathway_data)
            self.assertIn("aging_relevance", pathway_data)
    
    def test_compound_addition(self):
        """Test adding compounds to knowledge graph"""
        compound_name = "Test Compound"
        smiles = "CC(=O)O"  # Simple SMILES
        prediction_score = 0.75
        
        node_id = self.knowledge_graph.add_compound_to_graph(compound_name, smiles, prediction_score)
        
        self.assertIsInstance(node_id, str)
        self.assertIn(node_id, self.knowledge_graph.node_index)
        
        # Check node properties
        node = self.knowledge_graph.node_index[node_id]
        self.assertEqual(node.name, compound_name)
        self.assertEqual(node.type, "compound")
        self.assertEqual(node.properties["smiles"], smiles)
    
    def test_pathway_analysis(self):
        """Test compound pathway analysis"""
        # Test with known compound
        pathway_analyses = self.knowledge_graph.analyze_compound_pathways("metformin")
        
        self.assertIsInstance(pathway_analyses, list)
        if pathway_analyses:  # If metformin is in database
            analysis = pathway_analyses[0]
            self.assertIsInstance(analysis, PathwayAnalysis)
            self.assertIsInstance(analysis.pathway_score, float)
            self.assertIsInstance(analysis.affected_genes, list)
    
    def test_disease_risk_analysis(self):
        """Test disease risk factor analysis"""
        # Test lifestyle factors
        lifestyle_factors = {
            "sleep_hours": 6.0,  # Below optimal
            "steps": 5000,       # Below optimal
            "calories": 2200,    # Normal
            "protein": 80        # Good
        }
        
        risk_scores = self.knowledge_graph.analyze_disease_risk_factors(lifestyle_factors)
        
        self.assertIsInstance(risk_scores, dict)
        self.assertGreater(len(risk_scores), 0)
        
        # Check that all scores are between 0 and 1
        for disease, score in risk_scores.items():
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 1.0)
    
    def test_intervention_targets(self):
        """Test intervention target identification"""
        targets = self.knowledge_graph.find_intervention_targets("metformin", {})
        
        self.assertIsInstance(targets, dict)
        self.assertIn("molecular_targets", targets)
        self.assertIn("pathway_targets", targets)
        self.assertIn("intervention_score", targets)
        
        # Check data types
        self.assertIsInstance(targets["molecular_targets"], list)
        self.assertIsInstance(targets["pathway_targets"], list)
        self.assertIsInstance(targets["intervention_score"], float)
    
    def test_mechanistic_explanation(self):
        """Test mechanistic explanation generation"""
        explanation = self.knowledge_graph.generate_mechanistic_explanation("metformin", 0.75)
        
        self.assertIsInstance(explanation, str)
        self.assertIn("Mechanistic Analysis", explanation)
        self.assertIn("metformin", explanation.lower())
    
    def test_pathway_network_analysis(self):
        """Test pathway network analysis for multiple compounds"""
        compounds = ["metformin", "rapamycin", "resveratrol"]
        
        network_analysis = self.knowledge_graph.get_pathway_network_analysis(compounds)
        
        self.assertIsInstance(network_analysis, dict)
        self.assertIn("total_pathways", network_analysis)
        self.assertIn("top_pathways", network_analysis)
        self.assertIn("synergy_score", network_analysis)
        
        # Check data types
        self.assertIsInstance(network_analysis["total_pathways"], int)
        self.assertIsInstance(network_analysis["top_pathways"], list)
        self.assertIsInstance(network_analysis["synergy_score"], float)

class TestEnhancedPrediction(unittest.TestCase):
    """Test enhanced prediction engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.prediction_engine = EnhancedPredictionEngine()
    
    def test_prediction_engine_initialization(self):
        """Test prediction engine initialization"""
        self.assertIsInstance(self.prediction_engine, EnhancedPredictionEngine)
        self.assertIsNotNone(self.prediction_engine.literature_analyzer)
        self.assertIsNotNone(self.prediction_engine.knowledge_graph)
    
    @patch('Biomni.biomni.integration.enhanced_prediction.predict_lifespan_effect')
    def test_enhanced_drug_prediction(self, mock_predict):
        """Test enhanced drug prediction"""
        # Mock ML prediction
        mock_predict.return_value = 0.75
        
        # Test without literature/pathways (to avoid external dependencies)
        analysis = self.prediction_engine.enhanced_drug_prediction(
            "Test Compound", "CC(=O)O", include_literature=False, include_pathways=False
        )
        
        self.assertIsInstance(analysis, ComprehensiveAnalysis)
        self.assertEqual(analysis.original_prediction, 0.75)
        self.assertIsInstance(analysis.ml_confidence, float)
        self.assertIsInstance(analysis.combined_confidence, float)
        self.assertIsInstance(analysis.evidence_strength, str)
    
    @patch('Biomni.biomni.integration.enhanced_prediction.predict_bio_age')
    def test_enhanced_lifestyle_prediction(self, mock_predict):
        """Test enhanced lifestyle prediction"""
        # Mock ML prediction
        mock_predict.return_value = 45.5
        
        lifestyle_factors = {
            "sleep_hours": 7.5,
            "steps": 8000,
            "calories": 2200,
            "protein": 80
        }
        
        # Test without literature/pathways
        analysis = self.prediction_engine.enhanced_lifestyle_prediction(
            lifestyle_factors, include_literature=False, include_pathways=False
        )
        
        self.assertIsInstance(analysis, ComprehensiveAnalysis)
        self.assertEqual(analysis.original_prediction, 45.5)
        self.assertIsInstance(analysis.combined_confidence, float)
    
    def test_ml_confidence_calculation(self):
        """Test ML confidence calculation"""
        # Test probability confidence
        high_conf = self.prediction_engine._calculate_ml_confidence(0.9, "probability")
        low_conf = self.prediction_engine._calculate_ml_confidence(0.5, "probability")
        
        self.assertGreater(high_conf, low_conf)
        self.assertGreaterEqual(high_conf, 0.0)
        self.assertLessEqual(high_conf, 1.0)
        
        # Test bio age confidence
        bio_age_conf = self.prediction_engine._calculate_ml_confidence(45.0, "bio_age")
        self.assertEqual(bio_age_conf, 0.7)
    
    def test_combined_confidence_calculation(self):
        """Test combined confidence calculation"""
        # Mock evidence
        mock_literature = Mock()
        mock_literature.combined_confidence = 0.8
        
        mock_pathway = Mock()
        mock_pathway.pathway_score = 0.85
        
        combined_conf = self.prediction_engine._calculate_combined_confidence(
            0.75, mock_literature, [mock_pathway]
        )
        
        self.assertIsInstance(combined_conf, float)
        self.assertGreaterEqual(combined_conf, 0.0)
        self.assertLessEqual(combined_conf, 1.0)
    
    def test_evidence_strength_assessment(self):
        """Test evidence strength assessment"""
        # Mock evidence
        mock_literature = Mock()
        mock_literature.literature_evidence = ["evidence1", "evidence2"]
        mock_literature.combined_confidence = 0.8
        
        mock_pathway = Mock()
        mock_pathway.pathway_score = 0.85
        
        strength = self.prediction_engine._assess_evidence_strength(
            0.8, mock_literature, [mock_pathway]
        )
        
        self.assertIn(strength, ["Very Strong", "Strong", "Moderate", "Weak"])
    
    def test_multimodal_analysis_structure(self):
        """Test multi-modal analysis structure"""
        patient_data = {
            "patient_id": "TEST_001",
            "lifestyle": {
                "sleep_hours": 7.0,
                "steps": 8000,
                "calories": 2200,
                "protein": 80
            }
        }
        
        # Mock the prediction methods to avoid external dependencies
        with patch.object(self.prediction_engine, 'enhanced_lifestyle_prediction') as mock_lifestyle:
            mock_analysis = Mock()
            mock_analysis.original_prediction = 45.0
            mock_analysis.combined_confidence = 0.75
            mock_analysis.evidence_strength = "Strong"
            mock_lifestyle.return_value = mock_analysis
            
            results = self.prediction_engine.multi_modal_health_analysis(patient_data)
            
            self.assertIsInstance(results, dict)
            self.assertIn("lifestyle", results)
            self.assertIn("integrated_insights", results)

def run_phase2_tests():
    """Run all Phase 2 tests"""
    print("🧬 Running Phase 2: Deep Scientific Integration Tests")
    print("=" * 70)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestLiteratureEngine,
        TestKnowledgeGraph,
        TestEnhancedPrediction
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("✅ All Phase 2 tests passed! Deep scientific integration is working correctly.")
        print("\n🚀 Phase 2 Implementation Status:")
        print("✅ Literature analysis engine: TESTED")
        print("✅ Knowledge graph integration: TESTED")
        print("✅ Enhanced prediction engine: TESTED")
        print("✅ Multi-modal analysis: TESTED")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_phase2_tests()
    sys.exit(0 if success else 1)
