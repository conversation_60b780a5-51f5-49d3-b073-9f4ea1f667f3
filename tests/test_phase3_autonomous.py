"""
Test Suite for Phase 3: Autonomous Research Assistant
Tests autonomous research workflows, personalized health intelligence, and multi-step workflows

This module contains comprehensive tests for Phase 3 implementation,
including autonomous agents, workflow engine, and health intelligence system.
"""

import unittest
import sys
import os
import asyncio
from unittest.mock import Mock, patch, MagicMock
import json
from datetime import datetime

# Add paths for imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../Biomni')))

try:
    from Biomni.biomni.research.workflow_engine import (
        ResearchWorkflowEngine, WorkflowDefinition, WorkflowStep, WorkflowStatus, StepStatus
    )
    from Biomni.biomni.research.health_intelligence import (
        PersonalizedHealthIntelligence, HealthProfile, HealthAssessment, PersonalizedIntervention
    )
    from Biomni.biomni.research.autonomous_agent import (
        AutonomousResearchAgent, LongevityResearchAgent, CompoundResearchAgent, 
        PersonalizedMedicineAgent, ResearchHypothesis, HypothesisType, ResearchPriority
    )
except ImportError as e:
    print(f"Warning: Could not import Phase 3 modules for testing: {e}")

class TestWorkflowEngine(unittest.TestCase):
    """Test research workflow engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.workflow_engine = ResearchWorkflowEngine()
    
    def test_workflow_engine_initialization(self):
        """Test workflow engine initialization"""
        self.assertIsInstance(self.workflow_engine, ResearchWorkflowEngine)
        self.assertIsInstance(self.workflow_engine.workflows, dict)
        self.assertIsInstance(self.workflow_engine.executions, dict)
        self.assertIsInstance(self.workflow_engine.step_registry, dict)
        self.assertGreater(len(self.workflow_engine.step_registry), 0)
    
    def test_step_registry_initialization(self):
        """Test step registry contains required steps"""
        expected_steps = [
            "literature_search", "pathway_analysis", "compound_screening",
            "enhanced_prediction", "risk_assessment", "evidence_synthesis"
        ]
        
        for step_name in expected_steps:
            self.assertIn(step_name, self.workflow_engine.step_registry)
            self.assertTrue(callable(self.workflow_engine.step_registry[step_name]))
    
    def test_compound_discovery_workflow_creation(self):
        """Test compound discovery workflow creation"""
        research_question = "Find novel longevity compounds"
        target_properties = {"longevity_focus": True, "safety_profile": "good"}
        
        workflow_id = self.workflow_engine.create_compound_discovery_workflow(
            research_question, target_properties
        )
        
        self.assertIsInstance(workflow_id, str)
        self.assertIn(workflow_id, self.workflow_engine.workflows)
        
        workflow = self.workflow_engine.workflows[workflow_id]
        self.assertEqual(workflow.research_question, research_question)
        self.assertGreater(len(workflow.steps), 0)
        self.assertEqual(workflow.name, "Autonomous Compound Discovery")
    
    def test_personalized_health_workflow_creation(self):
        """Test personalized health workflow creation"""
        patient_data = {
            "patient_id": "test_patient",
            "lifestyle": {"sleep_hours": 7, "steps": 8000},
            "clinical": {"age": 45}
        }
        health_goals = ["longevity", "disease_prevention"]
        
        workflow_id = self.workflow_engine.create_personalized_health_workflow(
            patient_data, health_goals
        )
        
        self.assertIsInstance(workflow_id, str)
        self.assertIn(workflow_id, self.workflow_engine.workflows)
        
        workflow = self.workflow_engine.workflows[workflow_id]
        self.assertIn("longevity", workflow.research_question.lower())
        self.assertGreater(len(workflow.steps), 0)
    
    def test_workflow_step_dependencies(self):
        """Test workflow step dependency validation"""
        workflow_id = self.workflow_engine.create_compound_discovery_workflow(
            "Test workflow", {"test": True}
        )
        
        workflow = self.workflow_engine.workflows[workflow_id]
        
        # Check that steps with dependencies reference valid step IDs
        all_step_ids = {step.step_id for step in workflow.steps}
        
        for step in workflow.steps:
            for dependency in step.dependencies:
                self.assertIn(dependency, all_step_ids, 
                            f"Step {step.step_id} has invalid dependency: {dependency}")
    
    def test_workflow_step_execution_simulation(self):
        """Test individual workflow step execution"""
        # Test literature search step
        result = self.workflow_engine._step_literature_search(
            query="test query",
            compound_focus=True,
            max_papers=10
        )
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result.get("completion_status"), "success")
        self.assertIn("search_results", result)
        self.assertIn("total_papers", result)
    
    def test_compound_screening_step(self):
        """Test compound screening step"""
        target_properties = {"longevity_focus": True}
        screening_threshold = 0.6
        
        result = self.workflow_engine._step_compound_screening(
            target_properties=target_properties,
            screening_threshold=screening_threshold
        )
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result.get("completion_status"), "success")
        self.assertIn("top_compounds", result)
        self.assertIn("detailed_results", result)
        self.assertGreaterEqual(result.get("screening_threshold"), screening_threshold)

class TestHealthIntelligence(unittest.TestCase):
    """Test personalized health intelligence system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.health_intelligence = PersonalizedHealthIntelligence()
    
    def test_health_intelligence_initialization(self):
        """Test health intelligence system initialization"""
        self.assertIsInstance(self.health_intelligence, PersonalizedHealthIntelligence)
        self.assertIsInstance(self.health_intelligence.health_profiles, dict)
        self.assertIsInstance(self.health_intelligence.assessments, dict)
        self.assertIsInstance(self.health_intelligence.interventions, dict)
        self.assertIsInstance(self.health_intelligence.trajectories, dict)
    
    def test_health_profile_creation(self):
        """Test health profile creation"""
        user_id = "test_user_001"
        demographic_data = {"age": 35, "sex": "M", "height_cm": 175, "weight_kg": 70}
        lifestyle_data = {"sleep_hours": 7.5, "steps": 8000, "calories": 2200, "protein": 80}
        clinical_data = {"blood_pressure": "120/80", "cholesterol": 180}
        health_goals = ["longevity", "fitness"]
        
        profile_id = self.health_intelligence.create_health_profile(
            user_id=user_id,
            demographic_data=demographic_data,
            lifestyle_data=lifestyle_data,
            clinical_data=clinical_data,
            health_goals=health_goals
        )
        
        self.assertIsInstance(profile_id, str)
        self.assertIn(profile_id, self.health_intelligence.health_profiles)
        
        profile = self.health_intelligence.health_profiles[profile_id]
        self.assertEqual(profile.user_id, user_id)
        self.assertEqual(profile.age, 35)
        self.assertEqual(profile.health_goals, health_goals)
        self.assertIsInstance(profile.created_at, datetime)
    
    def test_comprehensive_assessment(self):
        """Test comprehensive health assessment"""
        # Create a test profile first
        profile_id = self.health_intelligence.create_health_profile(
            user_id="test_user_002",
            demographic_data={"age": 45, "sex": "F", "height_cm": 165, "weight_kg": 65},
            lifestyle_data={"sleep_hours": 6.5, "steps": 6000, "calories": 2000, "protein": 70},
            clinical_data={"blood_pressure": "130/85", "cholesterol": 200},
            health_goals=["disease_prevention"]
        )
        
        # Perform assessment
        assessment_id = self.health_intelligence.perform_comprehensive_assessment(profile_id)
        
        self.assertIsInstance(assessment_id, str)
        self.assertIn(profile_id, self.health_intelligence.assessments)
        
        assessments = self.health_intelligence.assessments[profile_id]
        self.assertGreater(len(assessments), 0)
        
        assessment = assessments[-1]  # Latest assessment
        self.assertEqual(assessment.profile_id, profile_id)
        self.assertIsInstance(assessment.biological_age, float)
        self.assertIsInstance(assessment.health_score, float)
        self.assertIsInstance(assessment.disease_risks, dict)
        self.assertGreaterEqual(assessment.health_score, 0)
        self.assertLessEqual(assessment.health_score, 100)
    
    def test_personalized_intervention_generation(self):
        """Test personalized intervention generation"""
        # Create profile and assessment
        profile_id = self.health_intelligence.create_health_profile(
            user_id="test_user_003",
            demographic_data={"age": 50, "sex": "M", "height_cm": 180, "weight_kg": 85},
            lifestyle_data={"sleep_hours": 6.0, "steps": 4000, "calories": 2500, "protein": 60},
            clinical_data={"blood_pressure": "140/90", "cholesterol": 240},
            health_goals=["cardiovascular_health", "weight_management"]
        )
        
        assessment_id = self.health_intelligence.perform_comprehensive_assessment(profile_id)
        
        # Generate interventions
        intervention_ids = self.health_intelligence.generate_personalized_interventions(profile_id)
        
        self.assertIsInstance(intervention_ids, list)
        self.assertGreater(len(intervention_ids), 0)
        self.assertIn(profile_id, self.health_intelligence.interventions)
        
        interventions = self.health_intelligence.interventions[profile_id]
        self.assertGreater(len(interventions), 0)
        
        # Check intervention properties
        intervention = interventions[0]
        self.assertIsInstance(intervention, PersonalizedIntervention)
        self.assertIn(intervention.intervention_type, ["lifestyle", "nutritional", "supplemental"])
        self.assertIsInstance(intervention.personalization_score, float)
        self.assertGreaterEqual(intervention.personalization_score, 0)
        self.assertLessEqual(intervention.personalization_score, 1)
    
    def test_health_trajectory_prediction(self):
        """Test health trajectory prediction"""
        # Create profile, assessment, and interventions
        profile_id = self.health_intelligence.create_health_profile(
            user_id="test_user_004",
            demographic_data={"age": 40, "sex": "F", "height_cm": 170, "weight_kg": 60},
            lifestyle_data={"sleep_hours": 8.0, "steps": 10000, "calories": 2100, "protein": 85},
            clinical_data={"blood_pressure": "115/75", "cholesterol": 170},
            health_goals=["longevity", "performance"]
        )
        
        assessment_id = self.health_intelligence.perform_comprehensive_assessment(profile_id)
        intervention_ids = self.health_intelligence.generate_personalized_interventions(profile_id)
        
        # Predict trajectory
        trajectory_id = self.health_intelligence.predict_health_trajectory(profile_id, intervention_ids)
        
        self.assertIsInstance(trajectory_id, str)
        self.assertIn(profile_id, self.health_intelligence.trajectories)
        
        trajectories = self.health_intelligence.trajectories[profile_id]
        self.assertGreater(len(trajectories), 0)
        
        trajectory = trajectories[-1]  # Latest trajectory
        self.assertEqual(trajectory.profile_id, profile_id)
        self.assertIsInstance(trajectory.predicted_biological_age, dict)
        self.assertIsInstance(trajectory.predicted_disease_risks, dict)
        self.assertIsInstance(trajectory.intervention_benefits, dict)
    
    def test_health_insights_generation(self):
        """Test comprehensive health insights generation"""
        # Create complete health profile with assessment and interventions
        profile_id = self.health_intelligence.create_health_profile(
            user_id="test_user_005",
            demographic_data={"age": 55, "sex": "M", "height_cm": 175, "weight_kg": 80},
            lifestyle_data={"sleep_hours": 7.0, "steps": 7000, "calories": 2300, "protein": 75},
            clinical_data={"blood_pressure": "135/85", "cholesterol": 210},
            health_goals=["disease_prevention", "longevity"]
        )
        
        assessment_id = self.health_intelligence.perform_comprehensive_assessment(profile_id)
        intervention_ids = self.health_intelligence.generate_personalized_interventions(profile_id)
        trajectory_id = self.health_intelligence.predict_health_trajectory(profile_id, intervention_ids)
        
        # Get comprehensive insights
        insights = self.health_intelligence.get_health_insights(profile_id)
        
        self.assertIsInstance(insights, dict)
        self.assertIn("profile_summary", insights)
        self.assertIn("health_trends", insights)
        self.assertIn("risk_priorities", insights)
        self.assertIn("intervention_effectiveness", insights)
        self.assertIn("personalized_recommendations", insights)
        self.assertIn("trajectory_summary", insights)
        
        # Check profile summary
        profile_summary = insights["profile_summary"]
        self.assertEqual(profile_summary["profile_id"], profile_id)
        self.assertIsInstance(profile_summary["biological_age"], float)
        self.assertIsInstance(profile_summary["health_score"], float)

class TestAutonomousAgent(unittest.TestCase):
    """Test autonomous research agents"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.longevity_agent = LongevityResearchAgent()
        self.compound_agent = CompoundResearchAgent()
        self.personalized_agent = PersonalizedMedicineAgent()
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        agents = [self.longevity_agent, self.compound_agent, self.personalized_agent]
        
        for agent in agents:
            self.assertIsInstance(agent, AutonomousResearchAgent)
            self.assertIsInstance(agent.agent_id, str)
            self.assertIsInstance(agent.specialization, str)
            self.assertIsInstance(agent.knowledge_base, dict)
            self.assertIsInstance(agent.hypothesis_registry, dict)
    
    def test_research_question_formulation(self):
        """Test research question formulation"""
        research_context = "Investigation of longevity interventions"
        
        question_id = self.longevity_agent.formulate_research_question(research_context)
        
        self.assertIsInstance(question_id, str)
        self.assertIn("question_", question_id)
    
    def test_hypothesis_generation(self):
        """Test hypothesis generation"""
        # First formulate a research question
        research_context = "Novel compound discovery for aging"
        question_id = self.compound_agent.formulate_research_question(research_context)
        
        # Generate hypothesis
        hypothesis_id = self.compound_agent.generate_hypothesis(question_id)
        
        self.assertIsInstance(hypothesis_id, str)
        self.assertIn(hypothesis_id, self.compound_agent.hypothesis_registry)
        
        hypothesis = self.compound_agent.hypothesis_registry[hypothesis_id]
        self.assertIsInstance(hypothesis, ResearchHypothesis)
        self.assertIsInstance(hypothesis.hypothesis_text, str)
        self.assertIsInstance(hypothesis.hypothesis_type, HypothesisType)
        self.assertIsInstance(hypothesis.confidence_score, float)
        self.assertIsInstance(hypothesis.priority, ResearchPriority)
        self.assertGreaterEqual(hypothesis.confidence_score, 0)
        self.assertLessEqual(hypothesis.confidence_score, 1)
    
    def test_experimental_design(self):
        """Test experimental design generation"""
        # Create research question and hypothesis
        research_context = "Personalized medicine optimization"
        question_id = self.personalized_agent.formulate_research_question(research_context)
        hypothesis_id = self.personalized_agent.generate_hypothesis(question_id)
        
        # Design experiment
        design_id = self.personalized_agent.design_experiment(hypothesis_id)
        
        self.assertIsInstance(design_id, str)
        self.assertIn("design_", design_id)
    
    def test_research_insight_generation(self):
        """Test research insight generation"""
        data_sources = ["literature", "knowledge_graph", "experimental"]
        analysis_focus = "patterns"
        
        insight_id = self.longevity_agent.generate_research_insights(data_sources, analysis_focus)
        
        self.assertIsInstance(insight_id, str)
        self.assertIn(insight_id, self.longevity_agent.insight_database)
        
        insight = self.longevity_agent.insight_database[insight_id]
        self.assertIsInstance(insight.insight_text, str)
        self.assertIn(insight.confidence_level, ["High", "Moderate", "Low"])
        self.assertIsInstance(insight.actionable_recommendations, list)
        self.assertIsInstance(insight.follow_up_questions, list)
    
    def test_agent_specialization_knowledge(self):
        """Test agent specialization knowledge bases"""
        # Longevity agent should have aging-related knowledge
        longevity_kb = self.longevity_agent.knowledge_base
        self.assertIn("aging_pathways", longevity_kb)
        self.assertIn("longevity_interventions", longevity_kb)
        
        # Compound agent should have drug-related knowledge
        compound_kb = self.compound_agent.knowledge_base
        self.assertIn("drug_targets", compound_kb)
        self.assertIn("pharmacokinetics", compound_kb)
        
        # Personalized agent should have personalization knowledge
        personalized_kb = self.personalized_agent.knowledge_base
        self.assertIn("genetic_factors", personalized_kb)
        self.assertIn("lifestyle_factors", personalized_kb)

def run_phase3_tests():
    """Run all Phase 3 tests"""
    print("🤖 Running Phase 3: Autonomous Research Assistant Tests")
    print("=" * 70)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestWorkflowEngine,
        TestHealthIntelligence,
        TestAutonomousAgent
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("✅ All Phase 3 tests passed! Autonomous research assistant is working correctly.")
        print("\n🚀 Phase 3 Implementation Status:")
        print("✅ Research workflow engine: TESTED")
        print("✅ Personalized health intelligence: TESTED")
        print("✅ Autonomous research agents: TESTED")
        print("✅ Multi-step workflow orchestration: TESTED")
        print("✅ Hypothesis generation and experimental design: TESTED")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_phase3_tests()
    sys.exit(0 if success else 1)
