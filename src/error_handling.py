"""
Comprehensive Error Handling Module for LifeMindML
Provides centralized error handling, logging, and recovery mechanisms
"""

import logging
import traceback
import sys
from typing import Dict, Any, Optional, Union
from datetime import datetime
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/lifemindml.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

logger = logging.getLogger(__name__)

class LifeMindMLError(Exception):
    """Base exception class for LifeMindML"""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "LIFEMINDML_ERROR"
        self.details = details or {}
        self.timestamp = datetime.now()
        super().__init__(self.message)

class ModelError(LifeMindMLError):
    """Exception for model-related errors"""
    def __init__(self, message: str, model_name: str = None, details: Dict[str, Any] = None):
        self.model_name = model_name
        super().__init__(message, "MODEL_ERROR", details)

class ValidationError(LifeMindMLError):
    """Exception for input validation errors"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        details = {"field": field, "value": str(value) if value is not None else None}
        super().__init__(message, "VALIDATION_ERROR", details)

class DataProcessingError(LifeMindMLError):
    """Exception for data processing errors"""
    def __init__(self, message: str, data_type: str = None, details: Dict[str, Any] = None):
        self.data_type = data_type
        super().__init__(message, "DATA_PROCESSING_ERROR", details)

class APIError(LifeMindMLError):
    """Exception for API-related errors"""
    def __init__(self, message: str, status_code: int = 500, details: Dict[str, Any] = None):
        self.status_code = status_code
        super().__init__(message, "API_ERROR", details)

class ErrorHandler:
    """Centralized error handling class"""
    
    def __init__(self):
        self.error_counts = {}
        self.error_log = []
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """Log error with context information"""
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "message": str(error),
            "traceback": traceback.format_exc(),
            "context": context or {}
        }
        
        # Add to error log
        self.error_log.append(error_info)
        
        # Keep only last 1000 errors
        if len(self.error_log) > 1000:
            self.error_log = self.error_log[-1000:]
        
        # Update error counts
        error_type = type(error).__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Log to file
        logger.error(f"Error occurred: {error_info}")
    
    def handle_model_error(self, error: Exception, model_name: str, input_data: Any = None) -> ModelError:
        """Handle model-specific errors"""
        context = {
            "model_name": model_name,
            "input_data_type": type(input_data).__name__ if input_data else None
        }
        
        self.log_error(error, context)
        
        # Create user-friendly error message
        if "CUDA" in str(error) or "GPU" in str(error):
            message = f"GPU processing error in {model_name} model. Falling back to CPU."
        elif "memory" in str(error).lower():
            message = f"Memory error in {model_name} model. Try with smaller input."
        elif "model" in str(error).lower() and "load" in str(error).lower():
            message = f"Failed to load {model_name} model. Please check model files."
        else:
            message = f"Prediction error in {model_name} model: {str(error)}"
        
        return ModelError(message, model_name, {"original_error": str(error)})
    
    def handle_validation_error(self, error: Exception, field: str = None, value: Any = None) -> ValidationError:
        """Handle input validation errors"""
        context = {
            "field": field,
            "value": str(value) if value is not None else None
        }
        
        self.log_error(error, context)
        
        message = f"Invalid input"
        if field:
            message += f" for field '{field}'"
        if value is not None:
            message += f" with value '{value}'"
        message += f": {str(error)}"
        
        return ValidationError(message, field, value)
    
    def handle_data_processing_error(self, error: Exception, data_type: str = None) -> DataProcessingError:
        """Handle data processing errors"""
        context = {"data_type": data_type}
        self.log_error(error, context)
        
        if "image" in str(error).lower() or data_type == "image":
            message = "Image processing failed. Please check image format and quality."
        elif "audio" in str(error).lower() or data_type == "audio":
            message = "Audio processing failed. Please check audio format and quality."
        elif "smiles" in str(error).lower() or data_type == "smiles":
            message = "SMILES processing failed. Please check molecular notation."
        else:
            message = f"Data processing failed: {str(error)}"
        
        return DataProcessingError(message, data_type, {"original_error": str(error)})
    
    def handle_api_error(self, error: Exception, endpoint: str = None) -> APIError:
        """Handle API-related errors"""
        context = {"endpoint": endpoint}
        self.log_error(error, context)
        
        if isinstance(error, HTTPException):
            return APIError(error.detail, error.status_code)
        elif "timeout" in str(error).lower():
            return APIError("Request timeout. Please try again.", 408)
        elif "connection" in str(error).lower():
            return APIError("Connection error. Please check your network.", 503)
        else:
            return APIError(f"API error: {str(error)}", 500)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        return {
            "total_errors": len(self.error_log),
            "error_counts": self.error_counts,
            "recent_errors": self.error_log[-10:] if self.error_log else []
        }
    
    def clear_error_log(self):
        """Clear error log (for maintenance)"""
        self.error_log.clear()
        self.error_counts.clear()
        logger.info("Error log cleared")

# Global error handler instance
error_handler = ErrorHandler()

def safe_execute(func, *args, **kwargs):
    """Safely execute a function with error handling"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_handler.log_error(e, {"function": func.__name__, "args": str(args), "kwargs": str(kwargs)})
        raise

def create_error_response(error: LifeMindMLError) -> JSONResponse:
    """Create standardized error response"""
    response_data = {
        "error": True,
        "error_code": error.error_code,
        "message": error.message,
        "timestamp": error.timestamp.isoformat(),
        "details": error.details
    }
    
    status_code = getattr(error, 'status_code', 500)
    return JSONResponse(content=response_data, status_code=status_code)

def handle_unexpected_error(error: Exception, context: str = None) -> JSONResponse:
    """Handle unexpected errors"""
    error_handler.log_error(error, {"context": context})
    
    response_data = {
        "error": True,
        "error_code": "UNEXPECTED_ERROR",
        "message": "An unexpected error occurred. Please try again later.",
        "timestamp": datetime.now().isoformat(),
        "details": {"context": context} if context else {}
    }
    
    return JSONResponse(content=response_data, status_code=500)

# Decorator for automatic error handling
def handle_errors(func):
    """Decorator to automatically handle errors in API endpoints"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            return create_error_response(e)
        except ModelError as e:
            return create_error_response(e)
        except DataProcessingError as e:
            return create_error_response(e)
        except APIError as e:
            return create_error_response(e)
        except HTTPException as e:
            # Re-raise FastAPI HTTPExceptions
            raise e
        except Exception as e:
            return handle_unexpected_error(e, func.__name__)
    
    return wrapper

# Recovery strategies
class RecoveryStrategies:
    """Strategies for recovering from common errors"""
    
    @staticmethod
    def retry_with_fallback(primary_func, fallback_func, max_retries: int = 3):
        """Retry primary function, fall back to secondary if all retries fail"""
        for attempt in range(max_retries):
            try:
                return primary_func()
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.info("All retries failed, using fallback")
                    return fallback_func()
        
    @staticmethod
    def graceful_degradation(func, default_response):
        """Execute function with graceful degradation to default response"""
        try:
            return func()
        except Exception as e:
            logger.warning(f"Function failed, using default response: {e}")
            return default_response
    
    @staticmethod
    def circuit_breaker(func, failure_threshold: int = 5, reset_timeout: int = 60):
        """Implement circuit breaker pattern"""
        # This is a simplified version - in production, use a proper circuit breaker library
        if not hasattr(circuit_breaker, 'failures'):
            circuit_breaker.failures = 0
            circuit_breaker.last_failure_time = None
        
        # Check if circuit is open
        if circuit_breaker.failures >= failure_threshold:
            if circuit_breaker.last_failure_time and \
               (datetime.now() - circuit_breaker.last_failure_time).seconds < reset_timeout:
                raise APIError("Service temporarily unavailable", 503)
            else:
                # Reset circuit breaker
                circuit_breaker.failures = 0
        
        try:
            result = func()
            circuit_breaker.failures = 0  # Reset on success
            return result
        except Exception as e:
            circuit_breaker.failures += 1
            circuit_breaker.last_failure_time = datetime.now()
            raise e

# Health check utilities
def check_model_health(model_name: str, model_object: Any) -> Dict[str, Any]:
    """Check health of a specific model"""
    try:
        if model_object is None:
            return {"status": "unhealthy", "reason": "Model not loaded"}
        
        # Basic health check - try a simple operation
        if hasattr(model_object, 'eval'):
            model_object.eval()
        
        return {"status": "healthy", "last_check": datetime.now().isoformat()}
    
    except Exception as e:
        return {
            "status": "unhealthy", 
            "reason": str(e),
            "last_check": datetime.now().isoformat()
        }

def system_health_check() -> Dict[str, Any]:
    """Comprehensive system health check"""
    health_status = {
        "overall_status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {},
        "error_stats": error_handler.get_error_stats()
    }
    
    # Check disk space
    try:
        import shutil
        disk_usage = shutil.disk_usage(".")
        free_space_gb = disk_usage.free / (1024**3)
        health_status["components"]["disk_space"] = {
            "status": "healthy" if free_space_gb > 1 else "warning",
            "free_space_gb": round(free_space_gb, 2)
        }
    except Exception as e:
        health_status["components"]["disk_space"] = {"status": "error", "reason": str(e)}
    
    # Check memory usage
    try:
        import psutil
        memory = psutil.virtual_memory()
        health_status["components"]["memory"] = {
            "status": "healthy" if memory.percent < 90 else "warning",
            "usage_percent": memory.percent
        }
    except ImportError:
        health_status["components"]["memory"] = {"status": "unknown", "reason": "psutil not available"}
    except Exception as e:
        health_status["components"]["memory"] = {"status": "error", "reason": str(e)}
    
    # Determine overall status
    component_statuses = [comp.get("status", "unknown") for comp in health_status["components"].values()]
    if "error" in component_statuses:
        health_status["overall_status"] = "unhealthy"
    elif "warning" in component_statuses:
        health_status["overall_status"] = "degraded"
    
    return health_status
