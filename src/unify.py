"""
LifeMindML Unified API Gateway
Combines all individual module APIs into a single FastAPI application
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
import uvicorn
import logging
import time
import os
import sys
from datetime import datetime
import tempfile
import io
from PIL import Image
import torch
import librosa
import numpy as np

# Add src to path for imports
sys.path.append(os.path.dirname(__file__))

# Import all model functions
try:
    from drug_model import predict_lifespan_effect
    from lifestyle_model import predict_bio_age
    from survival_model import predict_survival
    from disease_model import load_model as load_disease_model
    from voice_model import CNNEmotionClassifier
    from validation import validator, validate_and_raise
    from error_handling import error_handler, handle_errors, RecoveryStrategies, system_health_check
    from monitoring import (
        metrics_collector, performance_monitor, monitoring_dashboard,
        monitor_prediction, start_monitoring_scheduler
    )
except ImportError as e:
    logging.error(f"Error importing modules: {e}")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="🧠 LifeMindML Unified API",
    description="""
    **AI-Driven Personalized Health & Lifespan Optimization Platform**

    This unified API provides access to all LifeMindML modules:
    - 🧪 Drug Lifespan Classifier
    - 🩻 Disease Detection
    - 🧘 Lifestyle Optimizer
    - 📈 Survival Analysis
    - 🧏 Voice Emotion Detection

    Built with FastAPI, integrated with Stanford's Biomni framework.
    """,
    version="1.0.0",
    contact={
        "name": "LifeMindML Team",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model loading
disease_model = None
voice_model = None

# Pydantic models for request/response schemas
class DrugRequest(BaseModel):
    smiles: str = Field(..., description="SMILES notation of the molecule", example="CN(C)C(=N)N=C(N)N")

class DrugResponse(BaseModel):
    smiles: str
    lifespan_extension_probability: float
    interpretation: str
    confidence_level: str

class LifestyleRequest(BaseModel):
    sleep_hours: float = Field(..., ge=0, le=24, description="Hours of sleep per night")
    steps: int = Field(..., ge=0, description="Daily step count")
    calories: float = Field(..., ge=0, description="Daily caloric intake")
    protein: float = Field(..., ge=0, description="Daily protein intake in grams")

class LifestyleResponse(BaseModel):
    input: Dict[str, Any]
    predicted_biological_age: float
    health_score: float
    recommendations: List[str]

class SurvivalRequest(BaseModel):
    age: float = Field(..., ge=0, le=120)
    anaemia: int = Field(..., ge=0, le=1, description="0=No, 1=Yes")
    creatinine_phosphokinase: float = Field(..., ge=0)
    diabetes: int = Field(..., ge=0, le=1, description="0=No, 1=Yes")
    ejection_fraction: float = Field(..., ge=0, le=100)
    high_blood_pressure: int = Field(..., ge=0, le=1, description="0=No, 1=Yes")
    platelets: float = Field(..., ge=0)
    serum_creatinine: float = Field(..., ge=0)
    serum_sodium: float = Field(..., ge=0)
    sex: int = Field(..., ge=0, le=1, description="0=Female, 1=Male")
    smoking: int = Field(..., ge=0, le=1, description="0=No, 1=Yes")

class SurvivalResponse(BaseModel):
    input: Dict[str, Any]
    survival_curve: Dict[str, List[float]]
    median_survival_days: Optional[float]
    one_year_survival_probability: Optional[float]
    risk_level: str

class VoiceEmotionResponse(BaseModel):
    predicted_emotion: int
    emotion_label: str
    confidence: float
    emotion_probabilities: Dict[str, float]
    audio_characteristics: Dict[str, float]

class DiseaseResponse(BaseModel):
    prediction: str
    probability: float
    confidence_level: str
    recommendation: str

class HealthStatus(BaseModel):
    status: str
    timestamp: datetime
    version: str
    models_loaded: Dict[str, bool]

# Middleware for request logging and timing
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = time.time()

    # Log request
    logger.info(f"Request: {request.method} {request.url}")

    response = await call_next(request)

    # Log response time
    process_time = time.time() - start_time
    logger.info(f"Response time: {process_time:.4f}s")

    response.headers["X-Process-Time"] = str(process_time)
    return response

# Startup event to load models
@app.on_event("startup")
async def startup_event():
    """Load models on startup"""
    global disease_model, voice_model

    logger.info("Loading models...")

    try:
        # Load disease detection model
        disease_model = load_disease_model()
        logger.info("✅ Disease detection model loaded")
    except Exception as e:
        logger.error(f"❌ Failed to load disease model: {e}")

    try:
        # Load voice emotion model
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        voice_model = CNNEmotionClassifier().to(device)
        voice_model.load_state_dict(torch.load("models/voice_emotion.pth", map_location=device))
        voice_model.eval()
        logger.info("✅ Voice emotion model loaded")
    except Exception as e:
        logger.error(f"❌ Failed to load voice model: {e}")

    # Start monitoring scheduler
    start_monitoring_scheduler()
    logger.info("✅ Monitoring system started")

    logger.info("🚀 LifeMindML API startup complete")

# Health check endpoint
@app.get("/health", tags=["System"])
async def health_check():
    """Check API health and model status"""
    try:
        # Get comprehensive system health
        health_data = system_health_check()

        # Add model-specific health checks
        health_data["models"] = {
            "drug_classifier": {"status": "healthy", "reason": "Always available"},
            "disease_detection": {"status": "healthy" if disease_model is not None else "unhealthy",
                                "reason": "Model loaded" if disease_model else "Model not loaded"},
            "lifestyle_optimizer": {"status": "healthy", "reason": "Always available"},
            "survival_analysis": {"status": "healthy", "reason": "Always available"},
            "voice_emotion": {"status": "healthy" if voice_model is not None else "unhealthy",
                            "reason": "Model loaded" if voice_model else "Model not loaded"}
        }

        return health_data

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "overall_status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

# Root endpoint
@app.get("/", tags=["System"])
async def root():
    """Welcome message and API information"""
    return {
        "message": "🧠 Welcome to LifeMindML Unified API",
        "description": "AI-Driven Personalized Health & Lifespan Optimization Platform",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "modules": [
            "🧪 Drug Lifespan Classifier",
            "🩻 Disease Detection",
            "🧘 Lifestyle Optimizer",
            "📈 Survival Analysis",
            "🧏 Voice Emotion Detection"
        ]
    }

# Error monitoring endpoints
@app.get("/api/v1/system/errors", tags=["System"])
async def get_error_statistics():
    """Get error statistics and recent errors"""
    try:
        return error_handler.get_error_stats()
    except Exception as e:
        logger.error(f"Failed to get error stats: {e}")
        return {"error": "Failed to retrieve error statistics"}

@app.post("/api/v1/system/errors/clear", tags=["System"])
async def clear_error_log():
    """Clear error log (admin function)"""
    try:
        error_handler.clear_error_log()
        return {"message": "Error log cleared successfully"}
    except Exception as e:
        logger.error(f"Failed to clear error log: {e}")
        return {"error": "Failed to clear error log"}

# Monitoring endpoints
@app.get("/api/v1/monitoring/dashboard", tags=["📊 Monitoring"])
async def get_monitoring_dashboard():
    """Get comprehensive monitoring dashboard data"""
    try:
        return monitoring_dashboard.get_dashboard_data()
    except Exception as e:
        logger.error(f"Failed to get dashboard data: {e}")
        return {"error": "Failed to retrieve dashboard data"}

@app.get("/api/v1/monitoring/performance/{model_name}", tags=["📊 Monitoring"])
async def get_model_performance(model_name: str, days: int = 7):
    """Get performance metrics for a specific model"""
    try:
        performance = metrics_collector.get_model_performance(model_name, days)
        health = performance_monitor.check_model_health(model_name)

        return {
            "model_name": model_name,
            "performance": performance.__dict__,
            "health": health,
            "period_days": days
        }
    except Exception as e:
        logger.error(f"Failed to get model performance: {e}")
        return {"error": f"Failed to retrieve performance data for {model_name}"}

@app.get("/api/v1/monitoring/metrics", tags=["📊 Monitoring"])
async def get_prometheus_metrics():
    """Get Prometheus-compatible metrics"""
    try:
        report = performance_monitor.generate_performance_report()

        # Convert to Prometheus format
        metrics = []

        for model_name, model_data in report["models"].items():
            model_metrics = model_data["metrics"]

            metrics.append(f'lifemindml_predictions_total{{model="{model_name}"}} {model_metrics["total_predictions"]}')
            metrics.append(f'lifemindml_predictions_successful{{model="{model_name}"}} {model_metrics["successful_predictions"]}')
            metrics.append(f'lifemindml_predictions_failed{{model="{model_name}"}} {model_metrics["failed_predictions"]}')
            metrics.append(f'lifemindml_processing_time_avg{{model="{model_name}"}} {model_metrics["average_processing_time"]}')
            metrics.append(f'lifemindml_confidence_avg{{model="{model_name}"}} {model_metrics["average_confidence"]}')

            # Health status as numeric (1=healthy, 0.5=degraded, 0=unhealthy)
            health_score = 1.0 if model_data["status"] == "healthy" else 0.5 if model_data["status"] == "degraded" else 0.0
            metrics.append(f'lifemindml_model_health{{model="{model_name}"}} {health_score}')

        return "\n".join(metrics)

    except Exception as e:
        logger.error(f"Failed to generate metrics: {e}")
        return "# Error generating metrics"

@app.get("/api/v1/monitoring/alerts", tags=["📊 Monitoring"])
async def get_active_alerts():
    """Get active system alerts"""
    try:
        return monitoring_dashboard.get_active_alerts()
    except Exception as e:
        logger.error(f"Failed to get alerts: {e}")
        return {"error": "Failed to retrieve alerts"}

# Drug Lifespan Classifier Endpoints
@app.post("/api/v1/drug/predict", response_model=DrugResponse, tags=["🧪 Drug Classifier"])
@handle_errors
@monitor_prediction("drug_classifier")
async def predict_drug_lifespan(request: DrugRequest):
    """
    Predict lifespan extension probability for a molecule using SMILES notation.

    - **smiles**: SMILES string representation of the molecule
    - Returns probability score (0.0 to 1.0) and interpretation
    """
    # Validate input
    validation_result = validator.validate_drug_input(request.smiles)
    validate_and_raise(validation_result)

    try:
        # Use recovery strategy for robust prediction
        def primary_prediction():
            return predict_lifespan_effect(request.smiles)

        def fallback_prediction():
            logger.warning("Using fallback prediction for drug classifier")
            return 0.5  # Neutral prediction as fallback

        probability = RecoveryStrategies.retry_with_fallback(
            primary_prediction, fallback_prediction, max_retries=2
        )

        # Generate interpretation
        if probability > 0.7:
            interpretation = "High potential for lifespan extension"
            confidence_level = "High"
        elif probability > 0.5:
            interpretation = "Moderate potential for lifespan extension"
            confidence_level = "Moderate"
        else:
            interpretation = "Low potential for lifespan extension"
            confidence_level = "Low"

        return DrugResponse(
            smiles=request.smiles,
            lifespan_extension_probability=probability,
            interpretation=interpretation,
            confidence_level=confidence_level
        )

    except Exception as e:
        model_error = error_handler.handle_model_error(e, "drug_classifier", request.smiles)
        raise HTTPException(status_code=500, detail=model_error.message)

@app.get("/api/v1/drug/examples", tags=["🧪 Drug Classifier"])
async def get_drug_examples():
    """Get example molecules with their SMILES notation"""
    return {
        "examples": {
            "Metformin": "CN(C)C(=N)N=C(N)N",
            "Aspirin": "CC(=O)OC1=CC=CC=C1C(=O)O",
            "Resveratrol": "C1=CC(=CC=C1C=CC2=CC(=CC(=C2)O)O)O",
            "Rapamycin": "CC1CCC2CC(C(=CC=CC=CC(CC(C(=O)C(C(C(=CC(C(=O)CC(OC(=O)C3CCCCN3C(=O)C(=O)C1(O2)O)C(C)CC4CCC(C(C4)OC)O)C)C)O)OC)C)C)C)OC"
        }
    }

# Lifestyle Optimizer Endpoints
@app.post("/api/v1/lifestyle/predict", response_model=LifestyleResponse, tags=["🧘 Lifestyle Optimizer"])
@handle_errors
async def predict_lifestyle_age(request: LifestyleRequest):
    """
    Predict biological age based on lifestyle factors and provide recommendations.

    - **sleep_hours**: Hours of sleep per night (0-24)
    - **steps**: Daily step count
    - **calories**: Daily caloric intake
    - **protein**: Daily protein intake in grams
    """
    # Validate input
    validation_result = validator.validate_lifestyle_input(
        request.sleep_hours, request.steps, request.calories, request.protein
    )
    validate_and_raise(validation_result)

    try:
        features = {
            "sleep_hours": request.sleep_hours,
            "steps": request.steps,
            "calories": request.calories,
            "protein": request.protein
        }

        # Use graceful degradation for prediction
        def predict_with_fallback():
            try:
                return predict_bio_age(features)
            except Exception:
                # Fallback calculation based on simple heuristics
                base_age = 30
                sleep_factor = abs(request.sleep_hours - 8) * 2
                activity_factor = max(0, (10000 - request.steps) / 1000)
                return base_age + sleep_factor + activity_factor

        bio_age = RecoveryStrategies.graceful_degradation(
            lambda: predict_bio_age(features),
            predict_with_fallback()
        )

        # Calculate health score (simplified)
        health_score = max(0, min(100, 100 - abs(bio_age - 25) * 2))

        # Generate recommendations
        recommendations = []

        if request.sleep_hours < 7:
            recommendations.append("Increase sleep duration to 7-9 hours per night for optimal health")
        elif request.sleep_hours > 9:
            recommendations.append("Consider optimizing sleep quality rather than quantity")

        if request.steps < 8000:
            recommendations.append("Increase daily physical activity to reach 8,000-10,000 steps")

        if request.calories < 1800:
            recommendations.append("Consider increasing caloric intake to meet nutritional needs")
        elif request.calories > 2800:
            recommendations.append("Monitor portion sizes and focus on nutrient-dense foods")

        if request.protein < 60:
            recommendations.append("Increase protein intake to support muscle health and metabolism")

        if not recommendations:
            recommendations.append("Your lifestyle factors are well-balanced. Keep up the good work!")

        return LifestyleResponse(
            input=features,
            predicted_biological_age=bio_age,
            health_score=health_score,
            recommendations=recommendations
        )

    except Exception as e:
        model_error = error_handler.handle_model_error(e, "lifestyle_optimizer", features)
        raise HTTPException(status_code=500, detail=model_error.message)

# Survival Analysis Endpoints
@app.post("/api/v1/survival/predict", response_model=SurvivalResponse, tags=["📈 Survival Analysis"])
async def predict_patient_survival(request: SurvivalRequest):
    """
    Predict survival curves based on clinical factors using Cox proportional hazards model.

    Requires clinical parameters including age, medical conditions, and lab values.
    """
    try:
        features = {
            'age': float(request.age),
            'anaemia': request.anaemia,
            'creatinine_phosphokinase': float(request.creatinine_phosphokinase),
            'diabetes': request.diabetes,
            'ejection_fraction': float(request.ejection_fraction),
            'high_blood_pressure': request.high_blood_pressure,
            'platelets': float(request.platelets),
            'serum_creatinine': float(request.serum_creatinine),
            'serum_sodium': float(request.serum_sodium),
            'sex': request.sex,
            'smoking': request.smoking,
            'time': 100  # Required for schema but not used in prediction
        }

        survival_data = predict_survival(features)

        # Calculate key metrics
        median_survival = None
        one_year_survival = None

        # Find median survival time
        for i, prob in enumerate(survival_data['survival_probability']):
            if prob <= 0.5:
                median_survival = survival_data['time_days'][i]
                break

        # Find 1-year survival probability
        for i, time in enumerate(survival_data['time_days']):
            if time >= 365:
                one_year_survival = survival_data['survival_probability'][i]
                break

        # Determine risk level
        if median_survival and median_survival < 365:
            risk_level = "High"
        elif median_survival and median_survival < 730:
            risk_level = "Moderate"
        else:
            risk_level = "Low"

        return SurvivalResponse(
            input=features,
            survival_curve=survival_data,
            median_survival_days=median_survival,
            one_year_survival_probability=one_year_survival,
            risk_level=risk_level
        )

    except Exception as e:
        logger.error(f"Survival prediction error: {e}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

# Disease Detection Endpoints
@app.post("/api/v1/disease/predict", response_model=DiseaseResponse, tags=["🩻 Disease Detection"])
async def predict_disease(file: UploadFile = File(...)):
    """
    Analyze chest X-ray images for tuberculosis detection.

    - **file**: Chest X-ray image (PNG, JPG, JPEG)
    - Returns TB detection result with confidence score
    """
    if disease_model is None:
        raise HTTPException(status_code=503, detail="Disease detection model not available")

    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    try:
        # Read and process image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents)).convert("RGB")

        # Preprocess image (simplified - should match training preprocessing)
        from torchvision import transforms
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])

        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        image_tensor = transform(image).unsqueeze(0).to(device)

        # Make prediction
        with torch.no_grad():
            output = disease_model(image_tensor)
            probability = torch.sigmoid(output).item()

        prediction = "TB Positive" if probability > 0.5 else "TB Negative"

        # Generate confidence level and recommendation
        if probability > 0.8 or probability < 0.2:
            confidence_level = "High"
        elif probability > 0.6 or probability < 0.4:
            confidence_level = "Moderate"
        else:
            confidence_level = "Low"

        if prediction == "TB Positive":
            recommendation = "Consult with a healthcare professional immediately for further evaluation"
        else:
            recommendation = "No signs of TB detected, but this is not a substitute for professional medical diagnosis"

        return DiseaseResponse(
            prediction=prediction,
            probability=probability,
            confidence_level=confidence_level,
            recommendation=recommendation
        )

    except Exception as e:
        logger.error(f"Disease detection error: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

# Voice Emotion Detection Endpoints
@app.post("/api/v1/voice/predict", response_model=VoiceEmotionResponse, tags=["🧏 Voice Emotion"])
async def predict_voice_emotion(file: UploadFile = File(...)):
    """
    Analyze voice audio for emotion detection.

    - **file**: Audio file (WAV, MP3, FLAC)
    - Returns detected emotion with confidence scores
    """
    if voice_model is None:
        raise HTTPException(status_code=503, detail="Voice emotion model not available")

    # Validate file type
    if not file.content_type.startswith('audio/'):
        raise HTTPException(status_code=400, detail="File must be an audio file")

    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            contents = await file.read()
            tmp_file.write(contents)
            temp_path = tmp_file.name

        # Load and preprocess audio
        y, sr = librosa.load(temp_path, sr=None)

        # Extract MFCC features (should match training preprocessing)
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=40).T
        max_len = 200

        if mfcc.shape[0] < max_len:
            pad_width = max_len - mfcc.shape[0]
            mfcc = np.pad(mfcc, ((0, pad_width), (0, 0)), mode="constant")
        else:
            mfcc = mfcc[:max_len, :]

        # Convert to tensor
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        mfcc_tensor = torch.tensor(mfcc, dtype=torch.float32).unsqueeze(0).to(device)

        # Make prediction
        with torch.no_grad():
            outputs = voice_model(mfcc_tensor)
            probabilities = torch.softmax(outputs, dim=1).cpu().numpy()[0]
            predicted_idx = np.argmax(probabilities)

        # Emotion labels
        emotion_labels = {
            0: "Neutral", 1: "Calm", 2: "Happy", 3: "Sad",
            4: "Angry", 5: "Fearful", 6: "Disgust", 7: "Surprised"
        }

        # Audio characteristics
        duration = len(y) / sr
        rms_energy = np.sqrt(np.mean(y**2))
        zero_crossing_rate = np.mean(librosa.feature.zero_crossing_rate(y))

        # Clean up temp file
        os.unlink(temp_path)

        return VoiceEmotionResponse(
            predicted_emotion=int(predicted_idx),
            emotion_label=emotion_labels[predicted_idx],
            confidence=float(probabilities[predicted_idx]),
            emotion_probabilities={emotion_labels[i]: float(prob) for i, prob in enumerate(probabilities)},
            audio_characteristics={
                "duration_seconds": float(duration),
                "rms_energy": float(rms_energy),
                "zero_crossing_rate": float(zero_crossing_rate)
            }
        )

    except Exception as e:
        logger.error(f"Voice emotion detection error: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "unify:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )