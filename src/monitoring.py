"""
Model Monitoring and Performance Tracking System for LifeMindML
Provides comprehensive monitoring, metrics collection, and automated retraining capabilities
"""

import logging
import time
import json
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import numpy as np
import pandas as pd
from pathlib import Path
import pickle
import os
import schedule

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PredictionMetrics:
    """Data class for storing prediction metrics"""
    timestamp: datetime
    model_name: str
    input_hash: str
    prediction: Any
    confidence: float
    processing_time: float
    success: bool
    error_message: Optional[str] = None
    user_feedback: Optional[float] = None  # For collecting user feedback

@dataclass
class ModelPerformance:
    """Data class for model performance statistics"""
    model_name: str
    total_predictions: int
    successful_predictions: int
    failed_predictions: int
    average_processing_time: float
    average_confidence: float
    accuracy_score: Optional[float] = None
    last_updated: datetime = None

class MetricsCollector:
    """Collects and stores prediction metrics"""
    
    def __init__(self, db_path: str = "monitoring/metrics.db"):
        self.db_path = db_path
        self.setup_database()
        self.metrics_buffer = deque(maxlen=10000)  # In-memory buffer
        self.lock = threading.Lock()
    
    def setup_database(self):
        """Initialize SQLite database for metrics storage"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS prediction_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    model_name TEXT NOT NULL,
                    input_hash TEXT NOT NULL,
                    prediction TEXT,
                    confidence REAL,
                    processing_time REAL,
                    success BOOLEAN,
                    error_message TEXT,
                    user_feedback REAL
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_model_timestamp 
                ON prediction_metrics(model_name, timestamp)
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS model_performance (
                    model_name TEXT PRIMARY KEY,
                    total_predictions INTEGER,
                    successful_predictions INTEGER,
                    failed_predictions INTEGER,
                    average_processing_time REAL,
                    average_confidence REAL,
                    accuracy_score REAL,
                    last_updated TEXT
                )
            """)
    
    def record_prediction(self, metrics: PredictionMetrics):
        """Record a prediction metric"""
        with self.lock:
            self.metrics_buffer.append(metrics)
            
            # Periodically flush to database
            if len(self.metrics_buffer) >= 100:
                self.flush_to_database()
    
    def flush_to_database(self):
        """Flush metrics buffer to database"""
        if not self.metrics_buffer:
            return
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                metrics_data = []
                for metric in self.metrics_buffer:
                    metrics_data.append((
                        metric.timestamp.isoformat(),
                        metric.model_name,
                        metric.input_hash,
                        json.dumps(metric.prediction) if metric.prediction else None,
                        metric.confidence,
                        metric.processing_time,
                        metric.success,
                        metric.error_message,
                        metric.user_feedback
                    ))
                
                conn.executemany("""
                    INSERT INTO prediction_metrics 
                    (timestamp, model_name, input_hash, prediction, confidence, 
                     processing_time, success, error_message, user_feedback)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, metrics_data)
                
                self.metrics_buffer.clear()
                logger.info(f"Flushed {len(metrics_data)} metrics to database")
                
        except Exception as e:
            logger.error(f"Failed to flush metrics to database: {e}")
    
    def get_model_performance(self, model_name: str, days: int = 7) -> ModelPerformance:
        """Get performance statistics for a model"""
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed,
                    AVG(processing_time) as avg_time,
                    AVG(confidence) as avg_confidence
                FROM prediction_metrics 
                WHERE model_name = ? AND timestamp > ?
            """, (model_name, cutoff_date))
            
            row = cursor.fetchone()
            
            return ModelPerformance(
                model_name=model_name,
                total_predictions=row[0] or 0,
                successful_predictions=row[1] or 0,
                failed_predictions=row[2] or 0,
                average_processing_time=row[3] or 0.0,
                average_confidence=row[4] or 0.0,
                last_updated=datetime.now()
            )
    
    def get_recent_errors(self, model_name: str = None, limit: int = 50) -> List[Dict]:
        """Get recent prediction errors"""
        query = """
            SELECT timestamp, model_name, error_message, input_hash
            FROM prediction_metrics 
            WHERE success = 0
        """
        params = []
        
        if model_name:
            query += " AND model_name = ?"
            params.append(model_name)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(query, params)
            return [
                {
                    "timestamp": row[0],
                    "model_name": row[1],
                    "error_message": row[2],
                    "input_hash": row[3]
                }
                for row in cursor.fetchall()
            ]

class PerformanceMonitor:
    """Monitors model performance and triggers alerts"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.thresholds = {
            "min_success_rate": 0.95,
            "max_avg_processing_time": 5.0,
            "min_confidence": 0.6,
            "max_error_rate": 0.05
        }
        self.alerts = []
    
    def check_model_health(self, model_name: str) -> Dict[str, Any]:
        """Check health of a specific model"""
        performance = self.metrics_collector.get_model_performance(model_name)
        
        health_status = {
            "model_name": model_name,
            "status": "healthy",
            "issues": [],
            "metrics": asdict(performance)
        }
        
        # Check success rate
        if performance.total_predictions > 0:
            success_rate = performance.successful_predictions / performance.total_predictions
            if success_rate < self.thresholds["min_success_rate"]:
                health_status["status"] = "unhealthy"
                health_status["issues"].append(f"Low success rate: {success_rate:.2%}")
        
        # Check processing time
        if performance.average_processing_time > self.thresholds["max_avg_processing_time"]:
            health_status["status"] = "degraded"
            health_status["issues"].append(f"High processing time: {performance.average_processing_time:.2f}s")
        
        # Check confidence
        if performance.average_confidence < self.thresholds["min_confidence"]:
            health_status["status"] = "degraded"
            health_status["issues"].append(f"Low confidence: {performance.average_confidence:.2f}")
        
        return health_status
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        models = ["drug_classifier", "disease_detection", "lifestyle_optimizer", 
                 "survival_analysis", "voice_emotion"]
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "models": {},
            "overall_status": "healthy",
            "summary": {
                "total_predictions": 0,
                "total_errors": 0,
                "average_processing_time": 0.0
            }
        }
        
        total_predictions = 0
        total_errors = 0
        total_processing_time = 0.0
        
        for model_name in models:
            health = self.check_model_health(model_name)
            report["models"][model_name] = health
            
            if health["status"] != "healthy":
                report["overall_status"] = "degraded"
            
            # Aggregate metrics
            metrics = health["metrics"]
            total_predictions += metrics["total_predictions"]
            total_errors += metrics["failed_predictions"]
            total_processing_time += metrics["average_processing_time"]
        
        # Calculate summary
        if total_predictions > 0:
            report["summary"]["total_predictions"] = total_predictions
            report["summary"]["total_errors"] = total_errors
            report["summary"]["error_rate"] = total_errors / total_predictions
            report["summary"]["average_processing_time"] = total_processing_time / len(models)
        
        return report

class ModelRetrainer:
    """Handles automated model retraining based on performance metrics"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.retraining_thresholds = {
            "min_accuracy_drop": 0.05,  # Retrain if accuracy drops by 5%
            "min_predictions_for_retrain": 1000,
            "days_since_last_retrain": 30
        }
        self.retraining_history = {}
    
    def should_retrain_model(self, model_name: str) -> Tuple[bool, str]:
        """Determine if a model should be retrained"""
        performance = self.metrics_collector.get_model_performance(model_name, days=30)
        
        # Check if enough predictions have been made
        if performance.total_predictions < self.retraining_thresholds["min_predictions_for_retrain"]:
            return False, "Insufficient predictions for retraining assessment"
        
        # Check success rate
        success_rate = performance.successful_predictions / performance.total_predictions
        if success_rate < 0.9:  # Less than 90% success rate
            return True, f"Low success rate: {success_rate:.2%}"
        
        # Check if it's been too long since last retrain
        last_retrain = self.retraining_history.get(model_name)
        if last_retrain:
            days_since_retrain = (datetime.now() - last_retrain).days
            if days_since_retrain > self.retraining_thresholds["days_since_last_retrain"]:
                return True, f"Last retrained {days_since_retrain} days ago"
        
        return False, "Model performance is acceptable"
    
    def trigger_retraining(self, model_name: str) -> Dict[str, Any]:
        """Trigger retraining for a specific model"""
        logger.info(f"Triggering retraining for {model_name}")
        
        # This would typically trigger a retraining pipeline
        # For now, we'll just log and update the history
        self.retraining_history[model_name] = datetime.now()
        
        return {
            "model_name": model_name,
            "retrain_triggered": True,
            "timestamp": datetime.now().isoformat(),
            "status": "retraining_initiated"
        }

class MonitoringDashboard:
    """Provides monitoring dashboard functionality"""
    
    def __init__(self, metrics_collector: MetricsCollector, performance_monitor: PerformanceMonitor):
        self.metrics_collector = metrics_collector
        self.performance_monitor = performance_monitor
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get data for monitoring dashboard"""
        report = self.performance_monitor.generate_performance_report()
        recent_errors = self.metrics_collector.get_recent_errors(limit=10)
        
        return {
            "performance_report": report,
            "recent_errors": recent_errors,
            "system_status": self.get_system_status(),
            "alerts": self.get_active_alerts()
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        try:
            import psutil
            
            return {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "timestamp": datetime.now().isoformat()
            }
        except ImportError:
            return {"status": "system_monitoring_unavailable"}
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get active system alerts"""
        alerts = []
        
        # Check each model's health
        models = ["drug_classifier", "disease_detection", "lifestyle_optimizer", 
                 "survival_analysis", "voice_emotion"]
        
        for model_name in models:
            health = self.performance_monitor.check_model_health(model_name)
            if health["status"] != "healthy":
                alerts.append({
                    "type": "model_performance",
                    "severity": "warning" if health["status"] == "degraded" else "critical",
                    "model": model_name,
                    "issues": health["issues"],
                    "timestamp": datetime.now().isoformat()
                })
        
        return alerts

# Global monitoring instances
metrics_collector = MetricsCollector()
performance_monitor = PerformanceMonitor(metrics_collector)
model_retrainer = ModelRetrainer(metrics_collector)
monitoring_dashboard = MonitoringDashboard(metrics_collector, performance_monitor)

# Decorator for automatic metrics collection
def monitor_prediction(model_name: str):
    """Decorator to automatically monitor predictions"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            input_hash = str(hash(str(args) + str(kwargs)))
            
            try:
                result = func(*args, **kwargs)
                processing_time = time.time() - start_time
                
                # Extract confidence if available
                confidence = 1.0
                if isinstance(result, dict) and 'confidence' in result:
                    confidence = result['confidence']
                elif hasattr(result, 'confidence'):
                    confidence = result.confidence
                
                # Record successful prediction
                metrics = PredictionMetrics(
                    timestamp=datetime.now(),
                    model_name=model_name,
                    input_hash=input_hash,
                    prediction=result,
                    confidence=confidence,
                    processing_time=processing_time,
                    success=True
                )
                
                metrics_collector.record_prediction(metrics)
                return result
                
            except Exception as e:
                processing_time = time.time() - start_time
                
                # Record failed prediction
                metrics = PredictionMetrics(
                    timestamp=datetime.now(),
                    model_name=model_name,
                    input_hash=input_hash,
                    prediction=None,
                    confidence=0.0,
                    processing_time=processing_time,
                    success=False,
                    error_message=str(e)
                )
                
                metrics_collector.record_prediction(metrics)
                raise e
        
        return wrapper
    return decorator

# Scheduled tasks
def run_monitoring_tasks():
    """Run periodic monitoring tasks"""
    logger.info("Running monitoring tasks...")
    
    # Flush metrics to database
    metrics_collector.flush_to_database()
    
    # Check for models that need retraining
    models = ["drug_classifier", "disease_detection", "lifestyle_optimizer", 
             "survival_analysis", "voice_emotion"]
    
    for model_name in models:
        should_retrain, reason = model_retrainer.should_retrain_model(model_name)
        if should_retrain:
            logger.warning(f"Model {model_name} needs retraining: {reason}")
            # In production, this would trigger actual retraining
    
    logger.info("Monitoring tasks completed")

# Schedule monitoring tasks
schedule.every(1).hours.do(run_monitoring_tasks)
schedule.every(1).days.do(lambda: metrics_collector.flush_to_database())

def start_monitoring_scheduler():
    """Start the monitoring scheduler in a separate thread"""
    def run_scheduler():
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    logger.info("Monitoring scheduler started")
