"""
Input Validation and Error Handling Module for LifeMindML
Provides comprehensive validation for all input types and robust error handling
"""

import re
import numpy as np
import pandas as pd
from typing import Union, List, Dict, Any, Optional, Tuple
from rdkit import Chem
from PIL import Image
import librosa
import torch
import logging
from datetime import datetime
from pydantic import BaseModel, validator, ValidationError
from fastapi import HTTPException
import io

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom validation error class"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        self.message = message
        self.field = field
        self.value = value
        super().__init__(self.message)

class SMILESValidator:
    """Validator for SMILES molecular notation"""
    
    @staticmethod
    def validate_smiles(smiles: str) -> Tuple[bool, str]:
        """
        Validate SMILES string format and chemical validity
        
        Args:
            smiles: SMILES string to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(smiles, str):
            return False, "SMILES must be a string"
        
        if not smiles.strip():
            return False, "SMILES string cannot be empty"
        
        if len(smiles) > 1000:
            return False, "SMILES string too long (max 1000 characters)"
        
        # Check for invalid characters
        valid_chars = set("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789()[]{}=+-#@/\\.")
        if not set(smiles).issubset(valid_chars):
            invalid_chars = set(smiles) - valid_chars
            return False, f"Invalid characters in SMILES: {invalid_chars}"
        
        # Validate with RDKit
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False, "Invalid SMILES: Cannot parse molecular structure"
            
            # Additional chemical validity checks
            if mol.GetNumAtoms() == 0:
                return False, "Invalid SMILES: No atoms found"
            
            if mol.GetNumAtoms() > 200:
                return False, "Molecule too large (max 200 atoms)"
                
            return True, "Valid SMILES"
            
        except Exception as e:
            return False, f"SMILES validation error: {str(e)}"

class LifestyleValidator:
    """Validator for lifestyle input parameters"""
    
    @staticmethod
    def validate_sleep_hours(sleep_hours: float) -> Tuple[bool, str]:
        """Validate sleep hours input"""
        if not isinstance(sleep_hours, (int, float)):
            return False, "Sleep hours must be a number"
        
        if sleep_hours < 0:
            return False, "Sleep hours cannot be negative"
        
        if sleep_hours > 24:
            return False, "Sleep hours cannot exceed 24"
        
        if sleep_hours < 2:
            logger.warning(f"Unusually low sleep hours: {sleep_hours}")
        
        if sleep_hours > 16:
            logger.warning(f"Unusually high sleep hours: {sleep_hours}")
        
        return True, "Valid sleep hours"
    
    @staticmethod
    def validate_steps(steps: int) -> Tuple[bool, str]:
        """Validate daily steps input"""
        if not isinstance(steps, int):
            return False, "Steps must be an integer"
        
        if steps < 0:
            return False, "Steps cannot be negative"
        
        if steps > 100000:
            return False, "Steps value too high (max 100,000)"
        
        return True, "Valid steps"
    
    @staticmethod
    def validate_calories(calories: float) -> Tuple[bool, str]:
        """Validate calorie intake input"""
        if not isinstance(calories, (int, float)):
            return False, "Calories must be a number"
        
        if calories < 0:
            return False, "Calories cannot be negative"
        
        if calories < 500:
            logger.warning(f"Very low calorie intake: {calories}")
        
        if calories > 10000:
            return False, "Calorie value too high (max 10,000)"
        
        return True, "Valid calories"
    
    @staticmethod
    def validate_protein(protein: float) -> Tuple[bool, str]:
        """Validate protein intake input"""
        if not isinstance(protein, (int, float)):
            return False, "Protein must be a number"
        
        if protein < 0:
            return False, "Protein cannot be negative"
        
        if protein > 500:
            return False, "Protein value too high (max 500g)"
        
        return True, "Valid protein"

class ClinicalValidator:
    """Validator for clinical/survival analysis parameters"""
    
    @staticmethod
    def validate_age(age: float) -> Tuple[bool, str]:
        """Validate age input"""
        if not isinstance(age, (int, float)):
            return False, "Age must be a number"
        
        if age < 0:
            return False, "Age cannot be negative"
        
        if age > 150:
            return False, "Age too high (max 150)"
        
        if age < 18:
            logger.warning(f"Age below 18: {age}")
        
        return True, "Valid age"
    
    @staticmethod
    def validate_binary_field(value: int, field_name: str) -> Tuple[bool, str]:
        """Validate binary (0/1) fields"""
        if not isinstance(value, int):
            return False, f"{field_name} must be an integer"
        
        if value not in [0, 1]:
            return False, f"{field_name} must be 0 or 1"
        
        return True, f"Valid {field_name}"
    
    @staticmethod
    def validate_lab_value(value: float, field_name: str, min_val: float = 0, max_val: float = None) -> Tuple[bool, str]:
        """Validate laboratory values"""
        if not isinstance(value, (int, float)):
            return False, f"{field_name} must be a number"
        
        if value < min_val:
            return False, f"{field_name} cannot be less than {min_val}"
        
        if max_val and value > max_val:
            return False, f"{field_name} cannot exceed {max_val}"
        
        return True, f"Valid {field_name}"

class ImageValidator:
    """Validator for medical image inputs"""
    
    @staticmethod
    def validate_image(image_data: bytes, max_size_mb: int = 10) -> Tuple[bool, str]:
        """Validate uploaded image data"""
        if not image_data:
            return False, "No image data provided"
        
        # Check file size
        size_mb = len(image_data) / (1024 * 1024)
        if size_mb > max_size_mb:
            return False, f"Image too large ({size_mb:.1f}MB, max {max_size_mb}MB)"
        
        try:
            # Try to open image
            image = Image.open(io.BytesIO(image_data))
            
            # Check image format
            if image.format not in ['JPEG', 'PNG', 'TIFF']:
                return False, f"Unsupported image format: {image.format}"
            
            # Check image dimensions
            width, height = image.size
            if width < 50 or height < 50:
                return False, "Image too small (minimum 50x50 pixels)"
            
            if width > 5000 or height > 5000:
                return False, "Image too large (maximum 5000x5000 pixels)"
            
            # Check if image is grayscale or RGB
            if image.mode not in ['L', 'RGB', 'RGBA']:
                return False, f"Unsupported image mode: {image.mode}"
            
            return True, "Valid image"
            
        except Exception as e:
            return False, f"Invalid image data: {str(e)}"

class AudioValidator:
    """Validator for audio inputs"""
    
    @staticmethod
    def validate_audio(audio_data: bytes, max_size_mb: int = 50) -> Tuple[bool, str]:
        """Validate uploaded audio data"""
        if not audio_data:
            return False, "No audio data provided"
        
        # Check file size
        size_mb = len(audio_data) / (1024 * 1024)
        if size_mb > max_size_mb:
            return False, f"Audio file too large ({size_mb:.1f}MB, max {max_size_mb}MB)"
        
        try:
            # Save to temporary file and try to load with librosa
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                tmp_file.write(audio_data)
                temp_path = tmp_file.name
            
            # Try to load audio
            y, sr = librosa.load(temp_path, sr=None)
            
            # Clean up
            os.unlink(temp_path)
            
            # Check audio properties
            duration = len(y) / sr
            
            if duration < 0.5:
                return False, "Audio too short (minimum 0.5 seconds)"
            
            if duration > 300:  # 5 minutes
                return False, "Audio too long (maximum 5 minutes)"
            
            if sr < 8000:
                return False, "Sample rate too low (minimum 8kHz)"
            
            return True, "Valid audio"
            
        except Exception as e:
            return False, f"Invalid audio data: {str(e)}"

class ComprehensiveValidator:
    """Main validator class that combines all validation methods"""
    
    def __init__(self):
        self.smiles_validator = SMILESValidator()
        self.lifestyle_validator = LifestyleValidator()
        self.clinical_validator = ClinicalValidator()
        self.image_validator = ImageValidator()
        self.audio_validator = AudioValidator()
    
    def validate_drug_input(self, smiles: str) -> Dict[str, Any]:
        """Validate drug classifier input"""
        is_valid, message = self.smiles_validator.validate_smiles(smiles)
        
        return {
            "valid": is_valid,
            "message": message,
            "field": "smiles",
            "value": smiles
        }
    
    def validate_lifestyle_input(self, sleep_hours: float, steps: int, calories: float, protein: float) -> Dict[str, Any]:
        """Validate lifestyle optimizer input"""
        validations = [
            self.lifestyle_validator.validate_sleep_hours(sleep_hours),
            self.lifestyle_validator.validate_steps(steps),
            self.lifestyle_validator.validate_calories(calories),
            self.lifestyle_validator.validate_protein(protein)
        ]
        
        errors = []
        for i, (is_valid, message) in enumerate(validations):
            if not is_valid:
                field_names = ["sleep_hours", "steps", "calories", "protein"]
                errors.append({"field": field_names[i], "message": message})
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "message": "All inputs valid" if len(errors) == 0 else f"{len(errors)} validation errors"
        }
    
    def validate_survival_input(self, **kwargs) -> Dict[str, Any]:
        """Validate survival analysis input"""
        errors = []
        
        # Validate age
        if 'age' in kwargs:
            is_valid, message = self.clinical_validator.validate_age(kwargs['age'])
            if not is_valid:
                errors.append({"field": "age", "message": message})
        
        # Validate binary fields
        binary_fields = ['anaemia', 'diabetes', 'high_blood_pressure', 'sex', 'smoking']
        for field in binary_fields:
            if field in kwargs:
                is_valid, message = self.clinical_validator.validate_binary_field(kwargs[field], field)
                if not is_valid:
                    errors.append({"field": field, "message": message})
        
        # Validate lab values with specific ranges
        lab_validations = [
            ('ejection_fraction', 0, 100),
            ('creatinine_phosphokinase', 0, 10000),
            ('platelets', 50000, 1000000),
            ('serum_creatinine', 0.1, 20),
            ('serum_sodium', 100, 200)
        ]
        
        for field, min_val, max_val in lab_validations:
            if field in kwargs:
                is_valid, message = self.clinical_validator.validate_lab_value(
                    kwargs[field], field, min_val, max_val
                )
                if not is_valid:
                    errors.append({"field": field, "message": message})
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "message": "All inputs valid" if len(errors) == 0 else f"{len(errors)} validation errors"
        }
    
    def validate_image_input(self, image_data: bytes) -> Dict[str, Any]:
        """Validate disease detection image input"""
        is_valid, message = self.image_validator.validate_image(image_data)
        
        return {
            "valid": is_valid,
            "message": message,
            "field": "image"
        }
    
    def validate_audio_input(self, audio_data: bytes) -> Dict[str, Any]:
        """Validate voice emotion audio input"""
        is_valid, message = self.audio_validator.validate_audio(audio_data)
        
        return {
            "valid": is_valid,
            "message": message,
            "field": "audio"
        }

# Global validator instance
validator = ComprehensiveValidator()

def validate_and_raise(validation_result: Dict[str, Any]):
    """Helper function to validate and raise HTTPException if invalid"""
    if not validation_result["valid"]:
        if "errors" in validation_result:
            error_details = "; ".join([f"{err['field']}: {err['message']}" for err in validation_result["errors"]])
            raise HTTPException(status_code=422, detail=f"Validation failed: {error_details}")
        else:
            raise HTTPException(status_code=422, detail=f"Validation failed: {validation_result['message']}")
